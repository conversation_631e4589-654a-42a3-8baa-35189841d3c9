from deep_research.core.search_agent import SearchAgent
from deep_research.core.crawl_agent import CrawlAgent
from deep_research.core.synthesis_agent import SynthesisAgent

class DeepResearcher:
    def __init__(self, topic: str, mode: str, model_override=None, language='auto'):
        self.topic = topic
        self.mode = mode
        self.language = language
        self.search_agent = SearchAgent()
        self.crawl_agent = CrawlAgent()
        self.synthesis_agent = SynthesisAgent(model_override=model_override)

    async def run(self) -> str:
        # 1. Discover URLs
        print(f"🔍 [Phase 1/3] Searching for relevant sources...")
        urls = self.search_agent.search(self.topic, self.mode)
        if not urls:
            print("No relevant URLs found.")
            return ""
        print(f"Found {len(urls)} potential sources.")

        # 2. Crawl and Extract Content
        print(f"🕸️ [Phase 2/3] Crawling and extracting content...")
        contexts = await self.crawl_agent.crawl(urls)
        if not contexts:
            print("Failed to crawl content from sources.")
            return ""
        print(f"Successfully extracted content from {len(contexts)} sources.")

        # 3. Synthesize and Generate Report
        print(f"🧠 [Phase 3/3] Synthesizing information and generating report...")
        
        # 估算报告复杂度
        total_content_length = sum(len(content) for content in contexts.values())
        estimated_tokens = total_content_length // 4  # 粗略估算
        
        if estimated_tokens > 15000:
            print(f"📊 Large dataset detected (~{estimated_tokens} tokens). Report may be generated in multiple parts.")
        
        report = self.synthesis_agent.synthesize(self.topic, self.mode, contexts, self.language)
        
        if report:
            print(f"✅ Report generated successfully ({len(report)} characters)")
        
        return report




