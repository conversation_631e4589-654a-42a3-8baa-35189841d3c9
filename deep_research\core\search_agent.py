from ddgs import DDGS
from ddgs.exceptions import DDGSException, RatelimitException, TimeoutException
from deep_research.config.settings import DDGS_PROXY
from deep_research.utils.search_optimizer import SearchOptimizer
import time
import random
import os

class SearchAgent:
    def __init__(self):
        self.proxy = DDGS_PROXY or os.getenv("DDGS_PROXY")
        self.optimizer = SearchOptimizer()
        
    def search(self, topic: str, mode: str) -> list[str]:
        """执行搜索并返回URL列表，使用自适应搜索数量和备用策略"""
        # 计算最优搜索数量
        search_count = self.optimizer.calculate_search_count(topic, mode)
        
        # 首先尝试主要查询
        query = self._build_query(topic, mode)
        print(f"Using search query: {query}")
        print(f"Target search count: {search_count}")
        
        urls = self._try_search_with_fallback(topic, query, mode, search_count)
        
        # 如果主要查询失败，尝试简化查询
        if not urls:
            print("🔄 Primary search failed, trying simplified query...")
            simplified_query = self._build_simplified_query(topic, mode)
            print(f"Using simplified query: {simplified_query}")
            urls = self._try_search_with_fallback(topic, simplified_query, mode, search_count)
        
        # 如果还是失败，尝试最基本的查询
        if not urls:
            print("🔄 Simplified search failed, trying basic query...")
            basic_query = self._extract_main_keyword(topic)
            print(f"Using basic query: {basic_query}")
            urls = self._try_search_with_fallback(topic, basic_query, mode, min(search_count, 5))
        
        return urls
    
    def _try_search_with_fallback(self, topic: str, query: str, mode: str, search_count: int) -> list[str]:
        """尝试搜索，包含后端回退逻辑"""
        # 优化后端顺序，优先使用 auto，然后是更稳定的后端
        backends = ["auto", "google", "bing", "brave", "yandex", "yahoo"]
        
        for backend in backends:
            try:
                print(f"Trying backend: {backend}")
                urls = self._search_with_backend(topic, query, mode, backend, search_count)
                if urls:
                    # 过滤掉可能有问题的URL
                    filtered_urls = self._filter_problematic_urls(urls)
                    if filtered_urls:
                        return filtered_urls
                time.sleep(random.uniform(1, 3))
            except (RatelimitException, TimeoutException) as e:
                print(f"Backend {backend} failed with rate limit/timeout: {e}")
                time.sleep(random.uniform(3, 6))
            except DDGSException as e:
                print(f"Backend {backend} failed with DDGS error: {e}")
                time.sleep(random.uniform(2, 4))
            except Exception as e:
                print(f"Backend {backend} failed: {e}")
                time.sleep(random.uniform(1, 3))
        
        return []
    
    def _search_with_backend(self, topic: str, query: str, mode: str, backend: str, max_results: int) -> list[str]:
        """使用指定后端搜索"""
        try:
            # 根据文档，使用 "tb" 作为 Tor 代理的别名
            proxy = "tb" if self.proxy == "socks5://127.0.0.1:9150" else self.proxy
            
            # 初始化DDGS
            ddgs = DDGS(
                proxy=proxy,
                timeout=30,
                verify=True  # SSL验证
            )
            
            # 使用text搜索
            results = ddgs.text(
                query=query,
                region="wt-wt",  # 全球区域
                safesearch="off",  # 关闭安全搜索
                timelimit="y",    # 过去一年
                max_results=max_results,  # 使用自适应数量
                page=1,
                backend=backend
            )
            
            urls = []
            for r in results:
                if isinstance(r, dict) and 'href' in r:
                    urls.append(r['href'])
                elif hasattr(r, 'href'):
                    urls.append(r.href)
            
            if urls:
                print(f"Found {len(urls)} URLs using {backend} backend")
                return urls
                
        except Exception as e:
            print(f"Backend {backend} search failed: {e}")
            raise
        
        return []
    
    def _build_query(self, topic: str, mode: str) -> str:
        """根据模式构建搜索查询，优化中文和技术主题搜索"""
        
        # 检测是否为中文主题
        import re
        is_chinese = bool(re.search(r'[\u4e00-\u9fff]', topic))
        
        if mode == 'popular':
            if is_chinese:
                return f'{topic} 介绍 OR {topic} 是什么'
            else:
                return f'"{topic}" explained OR "what is {topic}"'
                
        elif mode == 'academic':
            if is_chinese:
                return f'{topic} 研究 OR {topic} 论文 filetype:pdf'
            else:
                return f'"{topic}" research paper OR study filetype:pdf site:arxiv.org'
                
        elif mode == 'engineering':
            # 对于技术主题，使用更简单的查询
            if is_chinese:
                # 提取英文技术名称（如crawl4ai）
                tech_terms = re.findall(r'[a-zA-Z0-9]+', topic)
                if tech_terms:
                    main_tech = tech_terms[0]  # 取第一个技术术语
                    return f'{main_tech} tutorial OR {main_tech} 使用方法 OR {main_tech} 教程'
                else:
                    return f'{topic} 教程 OR {topic} 使用方法'
            else:
                return f'"{topic}" tutorial OR implementation OR how-to'
        
        return topic
    
    def _build_simplified_query(self, topic: str, mode: str) -> str:
        """构建简化的搜索查询"""
        import re
        
        # 提取主要关键词
        if re.search(r'[\u4e00-\u9fff]', topic):  # 中文
            # 提取英文技术术语
            tech_terms = re.findall(r'[a-zA-Z0-9]+', topic)
            if tech_terms:
                return tech_terms[0]  # 只用主要技术术语
            else:
                # 提取中文关键词（去掉常见词汇）
                words = re.findall(r'[\u4e00-\u9fff]+', topic)
                filtered_words = [w for w in words if w not in ['的', '和', '与', '或', '及', '方法', '技巧', '使用']]
                return ' '.join(filtered_words[:2])  # 最多两个关键词
        else:
            # 英文：提取主要单词
            words = re.findall(r'\b[a-zA-Z]{3,}\b', topic.lower())
            return ' '.join(words[:2])  # 最多两个关键词
    
    def _extract_main_keyword(self, topic: str) -> str:
        """提取最主要的关键词"""
        import re
        
        # 提取技术术语（字母数字组合）
        tech_terms = re.findall(r'[a-zA-Z0-9]+', topic)
        if tech_terms:
            return tech_terms[0]
        
        # 提取中文主要词汇
        if re.search(r'[\u4e00-\u9fff]', topic):
            words = re.findall(r'[\u4e00-\u9fff]{2,}', topic)
            if words:
                return words[0]
        
        # 提取英文主要词汇
        words = re.findall(r'\b[a-zA-Z]{4,}\b', topic)
        if words:
            return words[0]
        
        return topic

    def _filter_problematic_urls(self, urls: list[str]) -> list[str]:
        """过滤掉可能有问题的URL"""
        filtered = []
        problematic_patterns = [
            'easypanel.host',  # 经常超时的域名
            'localhost',
            '127.0.0.1',
            'internal.',
            'private.'
        ]
        
        for url in urls:
            is_problematic = False
            for pattern in problematic_patterns:
                if pattern in url:
                    print(f"⚠️  Filtering potentially problematic URL: {url[:60]}...")
                    is_problematic = True
                    break
            
            if not is_problematic:
                filtered.append(url)
        
        return filtered



