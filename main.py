import click
import asyncio
from deep_research.core.researcher import DeepResearcher
from deep_research.utils.report_generator import save_report
from deep_research.config.settings import SUPPORTED_LANGUAGES

@click.command()
@click.argument('topic')
@click.option('--mode', type=click.Choice(['popular', 'academic', 'engineering']), default='popular', help='Type of research to perform.')
@click.option('--language', '--lang', type=click.Choice(list(SUPPORTED_LANGUAGES.keys())), default='auto', help='Output language (auto=detect from topic)')
@click.option('--model', help='Override the default LLM model')
def main(topic: str, mode: str, language: str, model: str):
    """
    Performs deep research on a given topic using Crawl4AI and DuckDuckGo.
    
    Examples:
    python main.py "人工智能" --mode popular --language zh
    python main.py "Artificial Intelligence" --mode academic --language en
    python main.py "機械学習" --mode engineering --language auto
    """
    lang_desc = SUPPORTED_LANGUAGES.get(language, language)
    click.echo(f"🚀 Starting {mode} research on topic: {topic}")
    click.echo(f"📝 Output language: {lang_desc}")
    if model:
        click.echo(f"🤖 Using model: {model}")
    
    researcher = DeepResearcher(topic, mode, model_override=model, language=language)
    report_content = asyncio.run(researcher.run())
    
    if report_content:
        save_report(topic, mode, report_content, language)
        click.echo(f"✅ Research complete. Report saved to output directory.")
    else:
        click.echo(f"❌ Research failed. Check logs for details.")

if __name__ == '__main__':
    main()


