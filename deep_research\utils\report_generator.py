"""Report generation and formatting utilities"""
import os
from datetime import datetime
from deep_research.config.settings import OUTPUT_DIR, SUPPORTED_LANGUAGES

def save_report(topic: str, mode: str, content: str, language: str = 'auto') -> None:
    """Save the research report to a markdown file"""
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    lang_suffix = f"_{language}" if language != 'auto' else ""
    filename = f"{topic}_{mode}_report{lang_suffix}_{timestamp}.md"
    filepath = os.path.join(OUTPUT_DIR, filename)
    
    # Format the final report
    lang_desc = SUPPORTED_LANGUAGES.get(language, language)
    report = f"""# {topic} - {mode.title()} Research Report

Generated on: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
Language: {lang_desc}

---

{content}

---

*Report generated by DeepResearch*
"""
    
    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"Report saved to: {filepath}")
