
---

### 1. 实现路径分析 (Implementation Path Analysis)

这个项目的核心在于将 **发现 (Discovery)**、**提取 (Extraction)**、**分析与综合 (Analysis & Synthesis)** 和 **报告生成 (Reporting)** 四个阶段有效结合。`duckduckgo-search` 负责发现，`crawl4ai` 负责提取，而一个大型语言模型 (LLM) 将负责分析与综合。

三个不同的调研模式（科普、学术、工程）将在每个阶段有不同的实现策略：

#### **阶段一：发现 (Discovery) - 使用 `duckduckgo-search`**

这是根据不同调研模式定制搜索策略的关键步骤。

1.  **科普调研 (Popular Science Research):**
    *   **目标:** 获取广泛、易于理解的背景知识。
    *   **搜索策略:**
        *   使用通用关键词，例如 `"{topic} explained"`, ` "what is {topic}"`, `"{topic} history"`。
        *   优先搜索维基百科、知名科普网站 (如 Scientific American, National Geographic)、高质量博客 (如 CSDN, Medium) 和新闻源。
        *   使用 `duckduckgo_search` 的 `text` 和 `news` 功能。

2.  **学术调研 (Academic Research):**
    *   **目标:** 查找学术论文、研究报告和专业论述。
    *   **搜索策略:**
        *   关键词中加入 "paper", "study", "research", "review"。
        *   使用 `duckduckgo_search` 的搜索运算符，重点是 `site:` 和 `filetype:`。
        *   `site:.edu OR site:.ac.uk OR site:arxiv.org OR site:researchgate.net OR site:semanticscholar.org`
        *   `filetype:pdf`
        *   这样可以高效地定位到学术论文和教育机构的资料。

3.  **工程调研 (Engineering Research):**
    *   **目标:** 寻找实际应用、代码实现、教程和最佳实践。
    *   **搜索策略:**
        *   关键词中加入 "tutorial", "how to", "implementation", "best practices", "API docs", "example code"。
        *   同样使用 `site:` 运算符，重点是 `site:github.com`, `site:stackoverflow.com`, `site:*.readthedocs.io`, 以及官方文档网站。
        *   搜索结果需要优先处理包含代码片段或技术指南的页面。

#### **阶段二：提取 (Extraction) - 使用 `crawl4ai`**

获取到URL列表后，`crawl4ai` 将负责高效地抓取和清理内容。

*   **统一策略:** 对所有模式，我们都使用 `AsyncWebCrawler` 的 `arun_many` 功能进行并发抓取，以提高效率。
*   **配置 `CrawlerRunConfig`:**
    *   **内容清理:** 使用 `PruningContentFilter` 或 `BM25ContentFilter` 过滤掉导航栏、页脚、广告等噪音，生成干净的 `fit_markdown`。这是至关重要的，因为它能显著减少后续LLM处理的Token数量和成本，并提高信息密度。
    *   **动态内容:** 对GitHub或文档类网站，可能需要开启 `js_code` 或 `scan_full_page` 来加载动态内容。
    *   **缓存:** 对于开发和测试，可以开启 `cache_mode=CacheMode.ENABLED`，在最终执行时使用 `CacheMode.BYPASS` 来获取最新信息。
    *   **错误处理:** 抓取结果中需要检查 `result.success`，并记录 `result.error_message`，以便在最终报告中指出哪些来源抓取失败。

#### **阶段三：分析与综合 (Analysis & Synthesis) - 使用 LLM**

这是项目的智能核心。将所有抓取到的干净Markdown内容整合起来，交给一个强大的LLM进行分析和总结。

*   **模型选择:** `crawl4ai` 内置的 `litellm` 支持可以让我们灵活选择模型，例如 `openai/gpt-4o` 用于高质量总结，或者 `groq/llama3-70b-8192` 用于快速响应。
*   **提示工程 (Prompt Engineering):** 为每种调研模式设计不同的系统提示 (System Prompt)。
    *   **科普模式提示:** "你是一位优秀的科普作家。请根据以下资料，为普通读者撰写一篇关于'{topic}'的介绍文章。内容应包括定义、历史背景、主要应用和未来展望。语言力求通俗易懂，并引用资料来源[REF_X]。"
    *   **学术模式提示:** "你是一位领域专家。请根据以下学术文献摘要，撰写一份关于'{topic}'的研究综述。内容应包括当前研究现状、主要方法论、关键发现、尚存争议或未解决的问题。格式要求严谨，并引用所有来源[REF_X]。"
    *   **工程模式提示:** "你是一位资深软件工程师。请根据以下技术文档和代码示例，为'{topic}'撰写一份实践指南。内容应包括核心概念、安装配置步骤、关键API/函数用法、代码示例和最佳实践。请引用资料来源[REF_X]。"
*   **上下文构建:** 将所有来源的Markdown内容合并，并为每个来源分配一个引用编号 (如 `[REF_1]`, `[REF_2]`)，然后注入到提示中。

#### **阶段四：报告生成 (Reporting)**

将LLM生成的内容格式化为一个结构清晰的Markdown报告。

*   **结构:** 报告应包含标题、摘要、正文（根据模式不同而变化）、以及一个完整的**参考文献**列表，列出每个`[REF_X]`对应的原始URL。
*   **输出:** 将报告保存为 `.md` 文件，文件名可以包含主题和调研模式，例如 `AI芯片_学术调研报告.md`。

---

### 2. 完整项目架构 (Project Architecture)

基于以上分析，我们可以构建如下的项目结构：

```
deep_research/
├── main.py                     # CLI入口，接收用户输入
├── pyproject.toml              # 项目依赖和元数据
├── README.md                   # 项目说明
├── .env                        # 存放API密钥
├── output/                     # 存放生成的调研报告
│   └── .gitkeep
├── deep_research/
│   ├── __init__.py
│   ├── core/
│   │   ├── __init__.py
│   │   ├── researcher.py       # 核心调度器 (DeepResearcher类)
│   │   ├── search_agent.py     # 搜索模块，负责生成搜索查询和URL
│   │   ├── crawl_agent.py      # 抓取模块，负责调用crawl4ai
│   │   └── synthesis_agent.py  # 综合模块，负责调用LLM进行总结
│   ├── config/
│   │   ├── __init__.py
│   │   ├── settings.py         # 全局配置，如默认模型、结果数量等
│   │   └── prompts.py          # 存放不同模式下的LLM提示模板
│   └── utils/
│       ├── __init__.py
│       ├── logging.py          # 日志配置
│       └── report_generator.py # 报告生成和格式化
└── tests/
    ├── __init__.py
    ├── test_search_agent.py
    └── test_researcher.py
```

#### **文件职责详解:**

*   **`main.py`**: 使用 `click` 或 `argparse` 创建命令行界面，用户可以输入如 `python main.py "AI芯片" --mode academic` 的命令。
*   **`researcher.py`**: 包含 `DeepResearcher` 类，是整个流程的编排者。它会依次调用 `SearchAgent`, `CrawlAgent`, 和 `SynthesisAgent`。
*   **`search_agent.py`**: 包含 `SearchAgent` 类，其核心方法 `search(topic, mode)` 会根据模式构建不同的DuckDuckGo查询语句，并返回一个URL列表。
*   **`crawl_agent.py`**: 包含 `CrawlAgent` 类，其核心方法 `crawl(urls)` 使用 `crawl4ai` 的 `arun_many` 对URL列表进行并发抓取，并返回清理后的Markdown内容和来源URL的映射。
*   **`synthesis_agent.py`**: 包含 `SynthesisAgent` 类，其核心方法 `synthesize(topic, mode, contexts)` 根据模式选择合适的提示，调用LLM处理抓取到的内容，并返回最终的报告主体。
*   **`settings.py`**: 存放可配置的常量，如 `SEARCH_RESULT_COUNT = 10`, `DEFAULT_LLM_PROVIDER = "openai/gpt-4o"`。
*   **`prompts.py`**: 存放为三种模式设计的详细LLM提示模板。
*   **`report_generator.py`**: 负责将 `SynthesisAgent` 的输出格式化为最终的Markdown文件，包括添加标题和参考文献列表。

---

### 3. 核心代码示例

#### `main.py` (CLI 入口)
```python
import click
import asyncio
from deep_research.core.researcher import DeepResearcher
from deep_research.utils.report_generator import save_report

@click.command()
@click.argument('topic')
@click.option('--mode', type=click.Choice(['popular', 'academic', 'engineering']), default='popular', help='Type of research to perform.')
def main(topic: str, mode: str):
    """
    Performs deep research on a given topic using Crawl4AI and DuckDuckGo.
    """
    click.echo(f"🚀 Starting {mode} research on topic: {topic}")
    
    researcher = DeepResearcher(topic, mode)
    report_content = asyncio.run(researcher.run())
    
    if report_content:
        save_report(topic, mode, report_content)
        click.echo(f"✅ Research complete. Report saved to output directory.")
    else:
        click.echo(f"❌ Research failed. Check logs for details.")

if __name__ == '__main__':
    main()
```

#### `deep_research/core/researcher.py` (核心调度器)
```python
from .search_agent import SearchAgent
from .crawl_agent import CrawlAgent
from .synthesis_agent import SynthesisAgent

class DeepResearcher:
    def __init__(self, topic: str, mode: str):
        self.topic = topic
        self.mode = mode
        self.search_agent = SearchAgent()
        self.crawl_agent = CrawlAgent()
        self.synthesis_agent = SynthesisAgent()

    async def run(self) -> str:
        # 1. Discover URLs
        print(f"🔍 [Phase 1/3] Searching for relevant sources...")
        urls = self.search_agent.search(self.topic, self.mode)
        if not urls:
            print("No relevant URLs found.")
            return ""
        print(f"Found {len(urls)} potential sources.")

        # 2. Crawl and Extract Content
        print(f"🕸️ [Phase 2/3] Crawling and extracting content...")
        contexts = await self.crawl_agent.crawl(urls)
        if not contexts:
            print("Failed to crawl content from sources.")
            return ""
        print(f"Successfully extracted content from {len(contexts)} sources.")

        # 3. Synthesize and Generate Report
        print(f"🧠 [Phase 3/3] Synthesizing information and generating report...")
        report = self.synthesis_agent.synthesize(self.topic, self.mode, contexts)
        
        return report
```

#### `deep_research/core/search_agent.py` (搜索模块)
```python
from duckduckgo_search import DDGS
from deep_research.config.settings import SEARCH_RESULT_COUNT

class SearchAgent:
    def search(self, topic: str, mode: str) -> list[str]:
        query = self._build_query(topic, mode)
        print(f"Using search query: {query}")

        results = DDGS().text(query, max_results=SEARCH_RESULT_COUNT)
        return [r['href'] for r in results]

    def _build_query(self, topic: str, mode: str) -> str:
        if mode == 'popular':
            return f'"{topic}" explained OR what is "{topic}" site:wikipedia.org OR site:medium.com OR site:towardsdatascience.com'
        elif mode == 'academic':
            return f'"{topic}" research paper OR study filetype:pdf site:.edu OR site:arxiv.org'
        elif mode == 'engineering':
            return f'"{topic}" tutorial OR implementation OR how-to site:github.com OR site:stackoverflow.com OR site:*.readthedocs.io'
        return topic
```

#### `deep_research/core/crawl_agent.py` (抓取模块)
```python
from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode
from crawl4ai.content_filter_strategy import PruningContentFilter
from crawl4ai.markdown_generation_strategy import DefaultMarkdownGenerator

class CrawlAgent:
    async def crawl(self, urls: list[str]) -> dict[str, str]:
        browser_config = BrowserConfig(headless=True, verbose=False)
        crawler_config = CrawlerRunConfig(
            cache_mode=CacheMode.ENABLED, # Use cache for development
            markdown_generator=DefaultMarkdownGenerator(
                content_filter=PruningContentFilter(threshold=0.5)
            )
        )
        
        contexts = {}
        async with AsyncWebCrawler(config=browser_config) as crawler:
            results = await crawler.arun_many(urls, config=crawler_config)
            for result in results:
                if result.success and result.markdown_v2 and result.markdown_v2.fit_markdown:
                    contexts[result.url] = result.markdown_v2.fit_markdown
                elif result.success and result.markdown:
                     contexts[result.url] = result.markdown
        return contexts
```

#### `deep_research/config/prompts.py` (提示模板)
```python
POPULAR_SCIENCE_PROMPT = """
You are an expert science communicator. Based on the following sources, write a comprehensive and easy-to-understand article about "{topic}".

Your article should cover:
1.  A clear definition and introduction.
2.  The history and background.
3.  Key applications and real-world examples.
4.  Future trends and implications.

Structure your response clearly with headings. For every claim you make, cite the source using the format [REF_X].

Sources:
{context}

Begin your article now.
"""

# ... (similar prompts for ACADEMIC_PROMPT and ENGINEERING_PROMPT)
```

这个架构遵循了单一职责原则，将不同阶段的逻辑分离开来，使得项目易于维护和扩展。例如，未来可以轻松地添加一个新的调研模式，只需在 `SearchAgent` 和 `SynthesisAgent` 中增加相应的逻辑即可。