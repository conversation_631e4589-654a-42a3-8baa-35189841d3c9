from crawl4ai import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>er<PERSON>onfig, CrawlerRunConfig, CacheMode
from crawl4ai.content_filter_strategy import PruningContentFilter
from crawl4ai.markdown_generation_strategy import DefaultMarkdownGenerator

class CrawlAgent:
    async def crawl(self, urls: list[str]) -> dict[str, str]:
        browser_config = BrowserConfig(
            headless=True, 
            verbose=False
        )
        crawler_config = CrawlerRunConfig(
            cache_mode=CacheMode.ENABLED,
            markdown_generator=DefaultMarkdownGenerator(
                content_filter=PruningContentFilter(threshold=0.5)
            ),
            check_robots_txt=True,
            page_timeout=60000
        )
        
        results = {}
        successful_crawls = 0
        
        async with Async<PERSON>ebCrawler(config=browser_config) as crawler:
            for i, url in enumerate(urls, 1):
                try:
                    print(f"[CRAWL {i}/{len(urls)}] Processing: {url[:60]}...")
                    
                    result = await crawler.arun(
                        url=url,
                        config=crawler_config
                    )
                    
                    if result.success and result.markdown:
                        # 处理不同的 markdown 格式
                        content = self._extract_markdown_content(result.markdown)
                        cleaned_content = self._clean_content(content)
                        
                        if len(cleaned_content.strip()) > 100:  # 确保内容有意义
                            results[url] = cleaned_content
                            successful_crawls += 1
                            print(f"✅ Successfully crawled: {url[:60]}...")
                        else:
                            print(f"⚠️  Content too short, skipping: {url[:60]}...")
                    else:
                        print(f"❌ Failed to crawl: {url[:60]}...")
                        if hasattr(result, 'error'):
                            print(f"   Error: {result.error}")
                
                except Exception as e:
                    print(f"❌ Exception crawling {url[:60]}...: {str(e)[:100]}")
                    continue
        
        print(f"Successfully extracted content from {successful_crawls} sources.")
        return results

    def _extract_markdown_content(self, markdown_result) -> str:
        """提取 markdown 内容，处理不同的返回格式"""
        if isinstance(markdown_result, str):
            return markdown_result
        elif hasattr(markdown_result, 'fit_markdown'):
            return markdown_result.fit_markdown
        elif hasattr(markdown_result, 'raw_markdown'):
            return markdown_result.raw_markdown
        else:
            return str(markdown_result)

    def _clean_content(self, content: str) -> str:
        """清理和验证内容"""
        if not content:
            return ""
        
        # 基本清理
        cleaned = content.strip()
        
        # 移除过多的空行
        import re
        cleaned = re.sub(r'\n\s*\n\s*\n', '\n\n', cleaned)
        
        return cleaned




