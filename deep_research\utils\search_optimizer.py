"""搜索优化工具 - 自适应调整搜索数量"""
import re
from deep_research.config.settings import ADAPTIVE_SEARCH_CONFIG, COMPLEXITY_KEYWORDS

class SearchOptimizer:
    @staticmethod
    def calculate_search_count(topic: str, mode: str) -> int:
        """
        根据主题复杂度和模式计算最优搜索数量
        
        Args:
            topic: 搜索主题
            mode: 研究模式 (popular/academic/engineering)
            
        Returns:
            int: 建议的搜索数量
        """
        config = ADAPTIVE_SEARCH_CONFIG.get(mode, ADAPTIVE_SEARCH_CONFIG['popular'])
        base_count = config['base_count']
        max_count = config['max_count']
        multiplier = config['complexity_multiplier']
        
        # 检测复杂度
        complexity_score = SearchOptimizer._detect_complexity(topic)
        
        # 计算调整后的数量
        adjusted_count = int(base_count * (1 + complexity_score * multiplier))
        
        # 确保在合理范围内
        final_count = min(max(adjusted_count, base_count), max_count)
        
        print(f"📊 Search optimization:")
        print(f"   Topic complexity: {SearchOptimizer._get_complexity_level(complexity_score)}")
        print(f"   Base count for {mode}: {base_count}")
        print(f"   Adjusted count: {final_count}")
        
        return final_count
    
    @staticmethod
    def _detect_complexity(topic: str) -> float:
        """
        检测主题复杂度
        
        Returns:
            float: 复杂度分数 (0.0 - 1.0)
        """
        topic_lower = topic.lower()
        
        # 计算不同复杂度关键词的匹配数
        high_matches = sum(1 for keyword in COMPLEXITY_KEYWORDS['high'] 
                          if keyword in topic_lower)
        medium_matches = sum(1 for keyword in COMPLEXITY_KEYWORDS['medium'] 
                            if keyword in topic_lower)
        low_matches = sum(1 for keyword in COMPLEXITY_KEYWORDS['low'] 
                         if keyword in topic_lower)
        
        # 主题长度因子 (更长的主题通常更复杂)
        length_factor = min(len(topic.split()) / 10.0, 0.3)
        
        # 技术术语检测 (包含专业术语的主题更复杂)
        tech_pattern = r'[A-Z]{2,}|[a-z]+[A-Z][a-z]*|[\u4e00-\u9fff]{4,}'
        tech_matches = len(re.findall(tech_pattern, topic))
        tech_factor = min(tech_matches / 5.0, 0.2)
        
        # 计算最终复杂度分数
        if high_matches > 0:
            complexity = 0.8 + length_factor + tech_factor
        elif medium_matches > 0:
            complexity = 0.5 + length_factor + tech_factor
        elif low_matches > 0:
            complexity = 0.2 + length_factor + tech_factor
        else:
            # 默认中等复杂度
            complexity = 0.4 + length_factor + tech_factor
        
        return min(complexity, 1.0)
    
    @staticmethod
    def _get_complexity_level(score: float) -> str:
        """获取复杂度等级描述"""
        if score >= 0.7:
            return "High (高)"
        elif score >= 0.4:
            return "Medium (中)"
        else:
            return "Low (低)"