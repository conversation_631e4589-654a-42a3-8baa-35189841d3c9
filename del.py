import torch
from transformers import pipeline, set_seed
from datasets import load_dataset
import pandas as pd
import logging
import os

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_default_device():
    """
    兼容性函数：为旧版本 PyTorch 提供 get_default_device 功能
    """
    # 直接实现逻辑，避免递归调用
    if torch.cuda.is_available():
        return torch.device('cuda')
    elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
        return torch.device('mps')
    else:
        return torch.device('cpu')

# 只有在 torch 没有 get_default_device 时才添加
if not hasattr(torch, 'get_default_device'):
    torch.get_default_device = get_default_device

def get_device():
    """获取可用的计算设备 (GPU或CPU)。"""
    if torch.cuda.is_available():
        device = "cuda"
        logging.info("CUDA GPU is available. Using GPU for generation.")
    elif torch.backends.mps.is_available(): # For Apple Silicon Macs
        device = "mps"
        logging.info("Apple Silicon MPS is available. Using MPS for generation.")
    else:
        device = "cpu"
        logging.info("No GPU/MPS available. Using CPU for generation. This might be slow.")
    return device

def quick_generate_pseudo_data(model_name: str = "gpt2", num_samples: int = 5):
    """
    快速生成指定数量的AG News伪数据。
    :param model_name: 用于文本生成的模型名称。
    :param num_samples: 要生成的伪数据样本数量。
    """
    device = get_device()
    set_seed(42) # 设置随机种子以保证结果可复现

    try:
        logging.info(f"Loading text generation pipeline with model: {model_name} on device: {device}")
        
        # 尝试不指定设备，让 accelerate 自动处理
        try:
            generator = pipeline(
                "text-generation",
                model=model_name
                # 移除 device 参数，让 accelerate 自动处理设备分配
            )
            logging.info("Text generation pipeline loaded successfully with accelerate.")
        except Exception as e:
            # 如果 accelerate 方式失败，尝试传统方式
            logging.warning(f"Accelerate loading failed: {e}. Trying traditional device assignment.")
            generator = pipeline(
                "text-generation",
                model=model_name,
                device=0 if device == "cuda" else -1 # 0 for first GPU, -1 for CPU
            )
            logging.info("Text generation pipeline loaded successfully with traditional method.")

        # AG News的类别映射
        agnews_labels = {
            0: "World",
            1: "Sports", 
            2: "Business",
            3: "Sci/Tech"
        }

        generated_data = []
        for i in range(num_samples):
            # 随机选择一个类别，并构建一个简单的提示
            # 实际应用中，提示会更复杂，以引导模型生成特定风格的文本
            category_id = i % len(agnews_labels) # 循环选择类别
            category_name = agnews_labels[category_id]
            prompt = f"Generate a news headline and a short description about {category_name}:"

            logging.info(f"Generating sample {i+1}/{num_samples} for category: {category_name}")
            # 生成文本，max_new_tokens控制生成长度，num_return_sequences控制返回数量
            # do_sample=True 启用采样，温度越高，生成文本越随机
            outputs = generator(
                prompt,
                max_new_tokens=50,
                num_return_sequences=1,
                do_sample=True,
                temperature=0.8,
                top_k=50,
                top_p=0.95,
                repetition_penalty=1.2 # 惩罚重复词语
            )

            if outputs:
                generated_text = outputs[0]['generated_text'].replace(prompt, '').strip()
                # 简单解析生成的文本，尝试分离标题和描述
                # 实际应用中可能需要更复杂的正则匹配或NLP解析
                parts = generated_text.split('\n', 1)
                headline = parts[0].strip() if parts else generated_text
                description = parts[1].strip() if len(parts) > 1 else ""

                generated_data.append({
                    "text": headline + " " + description, # 合并标题和描述
                    "label": category_id,
                    "label_name": category_name,
                    "source_prompt": prompt,
                    "raw_generated_text": generated_text
                })
                logging.info(f"Generated for {category_name}: '{headline[:50]}...'")
            else:
                logging.warning(f"No output generated for sample {i+1} with prompt: {prompt}")

        # 将生成的伪数据转换为DataFrame
        df = pd.DataFrame(generated_data)
        output_file = "agnews_quick_pseudo_data.csv"
        df.to_csv(output_file, index=False)
        logging.info(f"Generated {len(df)} pseudo samples and saved to {output_file}")

        print("\n--- Generated Pseudo Data (First 5 samples) ---")
        print(df.head())

    except Exception as e:
        logging.error(f"An error occurred during quick pseudo-data generation: {e}", exc_info=True)
        print("Please check the logs for more details on the error.")

if __name__ == "__main__":
    quick_generate_pseudo_data(num_samples=10) # 尝试生成10个样本
