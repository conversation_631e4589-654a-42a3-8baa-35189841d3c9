"""测试不同 LLM 模型的连接和响应"""
import os
import sys
import argparse
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from deep_research.config.settings import SUPPORTED_MODELS
import litellm

def check_api_key_availability(model_name: str) -> bool:
    """检查指定模型所需的 API 密钥是否可用"""
    if model_name.startswith("openai/"):
        return bool(os.getenv("OPENAI_API_KEY"))
    elif model_name.startswith("anthropic/"):
        return bool(os.getenv("ANTHROPIC_API_KEY"))
    elif model_name.startswith("gemini/"):
        return bool(os.getenv("GOOGLE_API_KEY"))
    elif model_name.startswith("deepseek/"):
        return bool(os.getenv("DEEPSEEK_API_KEY"))
    elif model_name.startswith("groq/"):
        return bool(os.getenv("GROQ_API_KEY"))
    return False

def test_model_direct(model_name: str):
    """直接测试模型，不通过 SynthesisAgent"""
    print(f"Testing model directly: {model_name}")
    
    # 检查 API 密钥
    if not check_api_key_availability(model_name):
        print(f"❌ No API key found for {model_name}")
        return False
    
    try:
        response = litellm.completion(
            model=model_name,
            messages=[
                {"role": "user", "content": "Say 'Hello, this is a test' and tell me which model you are."}
            ],
            max_tokens=100,
            temperature=0.1
        )
        
        result = response.choices[0].message.content
        print(f"✅ Model {model_name} works!")
        print(f"Response: {result}")
        return True
        
    except Exception as e:
        print(f"❌ Model {model_name} failed: {e}")
        return False

def test_model(model_name: str):
    """测试指定模型"""
    print(f"Testing model through SynthesisAgent: {model_name}")
    
    # 首先检查 API 密钥
    if not check_api_key_availability(model_name):
        print(f"❌ No API key found for {model_name}")
        print("Please set the appropriate environment variable:")
        if model_name.startswith("openai/"):
            print("  OPENAI_API_KEY=your_key_here")
        elif model_name.startswith("anthropic/"):
            print("  ANTHROPIC_API_KEY=your_key_here")
        elif model_name.startswith("gemini/"):
            print("  GOOGLE_API_KEY=your_key_here")
        elif model_name.startswith("deepseek/"):
            print("  DEEPSEEK_API_KEY=your_key_here")
        elif model_name.startswith("groq/"):
            print("  GROQ_API_KEY=your_key_here")
        return False
    
    try:
        from deep_research.core.synthesis_agent import SynthesisAgent
        
        # 直接传递模型名称给 SynthesisAgent
        agent = SynthesisAgent(model_override=model_name)
        
        # 简单的测试上下文
        test_contexts = {
            "https://example.com": "This is a test context about artificial intelligence and machine learning."
        }
        
        result = agent.synthesize(
            topic="AI测试",
            mode="popular",
            contexts=test_contexts
        )
        
        if result and not result.startswith("Error"):
            print(f"✅ Model {model_name} works!")
            print(f"Response preview: {result[:200]}...")
            return True
        else:
            print(f"❌ Model {model_name} failed: {result}")
            return False
            
    except Exception as e:
        print(f"❌ Model {model_name} failed with exception: {e}")
        return False

def list_supported_models():
    """列出所有支持的模型"""
    print("Supported models:")
    for provider, models in SUPPORTED_MODELS.items():
        print(f"\n{provider.upper()}:")
        for model in models:
            has_key = check_api_key_availability(model)
            status = "✅" if has_key else "❌"
            print(f"  {status} {model}")

def check_all_api_keys():
    """检查所有 API 密钥的可用性"""
    print("API Key Status:")
    print(f"OPENAI_API_KEY: {'✅' if os.getenv('OPENAI_API_KEY') else '❌'}")
    print(f"ANTHROPIC_API_KEY: {'✅' if os.getenv('ANTHROPIC_API_KEY') else '❌'}")
    print(f"GOOGLE_API_KEY: {'✅' if os.getenv('GOOGLE_API_KEY') else '❌'}")
    print(f"DEEPSEEK_API_KEY: {'✅' if os.getenv('DEEPSEEK_API_KEY') else '❌'}")
    print(f"GROQ_API_KEY: {'✅' if os.getenv('GROQ_API_KEY') else '❌'}")

def test_all_models():
    """测试所有配置的模型"""
    results = {}
    
    for provider, models in SUPPORTED_MODELS.items():
        print(f"\n--- Testing {provider.upper()} models ---")
        for model in models:
            results[model] = test_model(model)
    
    print("\n--- Test Summary ---")
    for model, success in results.items():
        status = "✅" if success else "❌"
        print(f"{status} {model}")
    
    return results

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Test LLM models")
    parser.add_argument("--model", help="Specific model to test")
    parser.add_argument("--list", action="store_true", help="List all supported models")
    parser.add_argument("--all", action="store_true", help="Test all models")
    parser.add_argument("--keys", action="store_true", help="Check API key availability")
    parser.add_argument("--direct", help="Test model directly with litellm")
    
    args = parser.parse_args()
    
    if args.keys:
        check_all_api_keys()
    elif args.list:
        list_supported_models()
    elif args.all:
        test_all_models()
    elif args.direct:
        test_model_direct(args.direct)
    elif args.model:
        test_model(args.model)
    else:
        print("Please specify --model, --list, --all, --keys, or --direct")
        parser.print_help()




