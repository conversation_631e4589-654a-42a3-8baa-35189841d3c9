# agnews数据集伪数据生成器下载 - Engineering Research Report

Generated on: 2025-07-21 14:34:29
Language: 中文

---

### 1. 概述与核心概念

AG News数据集是一个广泛用于文本分类任务的新闻文章数据集，它包含约12万条训练样本和7600条测试样本，分为四个类别：世界（World）、体育（Sports）、商业（Business）和科技（Sci/Tech）。每条样本由新闻标题和新闻描述组成。

“伪数据生成器”（Pseudo-data Generator）在此上下文中，指的是能够根据AG News数据集的特征（如新闻主题、语言风格和结构）自动生成新的、合成的或“伪造的”数据样本的工具或脚本。其核心目的是扩充现有数据集，尤其是在原始数据量不足、类别不平衡或需要增加模型泛化能力时。

**核心概念：**

*   **伪数据（Pseudo-data）**：通过算法或模型生成的人工数据，旨在模拟真实数据的分布和特征。它不是原始数据集的简单复制，而是基于原始数据学习到的模式创建的新样本。
*   **数据增强（Data Augmentation）**：通过对现有数据进行转换或生成新数据来扩充数据集的技术。伪数据生成是数据增强的一种高级形式。
*   **文本生成（Text Generation）**：利用大型语言模型（LLMs）根据给定的提示（prompt）或上下文生成连贯、有意义的文本。这是伪数据生成的核心技术。
*   **AG News数据集特性**：短文本（标题和描述），内容聚焦于特定新闻领域，语言风格相对正式。生成伪数据时需确保这些特性得以保留。

**为什么需要伪数据生成器？**

1.  **数据稀缺性**：在某些场景下，获取大量标注数据成本高昂或难以实现。伪数据可以有效弥补这一不足。
2.  **类别不平衡**：如果AG News数据集中某个类别的样本数量远少于其他类别，生成该类别的伪数据可以帮助平衡数据集，提升模型对少数类别的识别能力。
3.  **模型鲁棒性**：引入多样化的伪数据可以使训练出的模型对未见过的新闻文本更具鲁棒性。
4.  **隐私保护**：在某些敏感领域，直接使用真实数据可能存在隐私风险。伪数据可以在不暴露原始数据的情况下进行模型训练和测试。
5.  **快速原型验证与测试**：在没有真实数据或等待数据收集期间，可以使用伪数据进行模型开发和初步测试。

本指南将侧重于利用流行的自然语言处理（NLP）库，特别是基于Transformer架构的大型语言模型，来构建一个实用的AG News伪数据生成器，而非提供一个预编译的“下载”包，因为生成器的灵活性和可配置性对于满足不同需求至关重要。

### 2. 环境准备与安装

为了运行AG News伪数据生成器，您需要一个Python环境，并安装一些核心库。推荐使用Anaconda或Miniconda来管理Python环境。

1.  **创建Conda环境（推荐）**：
    ```bash
    conda create -n agnews_pseudo python=3.9
    conda activate agnews_pseudo
    ```
    这将创建一个名为 `agnews_pseudo` 的新环境，并激活它。

2.  **安装核心依赖**：
    我们将主要使用`transformers`库进行文本生成，`datasets`库用于加载AG News数据集，`torch`作为`transformers`的后端，以及`pandas`用于数据处理。
    ```bash
    pip install torch transformers datasets pandas accelerate sentencepiece
    ```
    *   `torch`: PyTorch深度学习框架，`transformers`的必要依赖。
    *   `transformers`: Hugging Face的Transformer库，提供预训练语言模型和文本生成功能。
    *   `datasets`: Hugging Face的Datasets库，方便加载和处理各种NLP数据集，包括AG News。
    *   `pandas`: 用于数据结构和数据分析，方便处理生成的表格数据。
    *   `accelerate`: 用于优化大型模型在多GPU或CPU上的运行。
    *   `sentencepiece`: 部分Transformer模型（如T5、LLaMA）需要它进行分词。

3.  **GPU支持（可选但强烈推荐）**：
    如果您的机器配备了NVIDIA GPU，并且您希望利用GPU加速文本生成过程，请确保您的PyTorch安装支持CUDA。您可以通过以下命令检查PyTorch的CUDA支持：
    ```python
    import torch
    print(torch.cuda.is_available())
    print(torch.cuda.device_count())
    print(torch.cuda.get_device_name(0) if torch.cuda.is_available() else "No GPU")
    ```
    如果`torch.cuda.is_available()`返回`True`，则表示CUDA已正确安装。如果返回`False`，您可能需要根据您的CUDA版本和PyTorch版本重新安装PyTorch。请参考PyTorch官方网站获取正确的安装命令。

    例如，对于CUDA 11.8：
    ```bash
    pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
    ```
    对于CPU版本：
    ```bash
    pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
    ```

4.  **验证安装**：
    安装完成后，您可以运行一个简单的Python脚本来验证主要库是否正确安装：
    ```python
    import torch
    import transformers
    import datasets
    import pandas as pd

    print(f"PyTorch version: {torch.__version__}")
    print(f"Transformers version: {transformers.__version__}")
    print(f"Datasets version: {datasets.__version__}")
    print(f"Pandas version: {pd.__version__}")
    print("All required libraries installed successfully!")
    ```
    ```
    ```

### 3. 快速开始

本节将展示如何快速加载AG News数据集，并使用一个简单的预训练语言模型生成几条伪数据。我们将使用`gpt2`模型作为示例，因为它相对较小，易于在大多数系统上运行。

```python
import torch
from transformers import pipeline, set_seed
from datasets import load_dataset
import pandas as pd
import logging
import os

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_device():
    """获取可用的计算设备 (GPU或CPU)。"""
    if torch.cuda.is_available():
        device = "cuda"
        logging.info("CUDA GPU is available. Using GPU for generation.")
    elif torch.backends.mps.is_available(): # For Apple Silicon Macs
        device = "mps"
        logging.info("Apple Silicon MPS is available. Using MPS for generation.")
    else:
        device = "cpu"
        logging.info("No GPU/MPS available. Using CPU for generation. This might be slow.")
    return device

def quick_generate_pseudo_data(model_name: str = "gpt2", num_samples: int = 5):
    """
    快速生成指定数量的AG News伪数据。
    :param model_name: 用于文本生成的模型名称。
    :param num_samples: 要生成的伪数据样本数量。
    """
    device = get_device()
    set_seed(42) # 设置随机种子以保证结果可复现

    try:
        logging.info(f"Loading text generation pipeline with model: {model_name} on device: {device}")
        # 使用pipeline简化文本生成过程
        generator = pipeline(
            "text-generation",
            model=model_name,
            device=0 if device == "cuda" else -1 # 0 for first GPU, -1 for CPU
        )
        logging.info("Text generation pipeline loaded successfully.")

        # AG News的类别映射
        agnews_labels = {
            0: "World",
            1: "Sports",
            2: "Business",
            3: "Sci/Tech"
        }

        generated_data = []
        for i in range(num_samples):
            # 随机选择一个类别，并构建一个简单的提示
            # 实际应用中，提示会更复杂，以引导模型生成特定风格的文本
            category_id = i % len(agnews_labels) # 循环选择类别
            category_name = agnews_labels[category_id]
            prompt = f"Generate a news headline and a short description about {category_name}:"

            logging.info(f"Generating sample {i+1}/{num_samples} for category: {category_name}")
            # 生成文本，max_new_tokens控制生成长度，num_return_sequences控制返回数量
            # do_sample=True 启用采样，温度越高，生成文本越随机
            outputs = generator(
                prompt,
                max_new_tokens=50,
                num_return_sequences=1,
                do_sample=True,
                temperature=0.8,
                top_k=50,
                top_p=0.95,
                repetition_penalty=1.2 # 惩罚重复词语
            )

            if outputs:
                generated_text = outputs[0]['generated_text'].replace(prompt, '').strip()
                # 简单解析生成的文本，尝试分离标题和描述
                # 实际应用中可能需要更复杂的正则匹配或NLP解析
                parts = generated_text.split('\n', 1)
                headline = parts[0].strip() if parts else generated_text
                description = parts[1].strip() if len(parts) > 1 else ""

                generated_data.append({
                    "text": headline + " " + description, # 合并标题和描述
                    "label": category_id,
                    "label_name": category_name,
                    "source_prompt": prompt,
                    "raw_generated_text": generated_text
                })
                logging.info(f"Generated for {category_name}: '{headline[:50]}...'")
            else:
                logging.warning(f"No output generated for sample {i+1} with prompt: {prompt}")

        # 将生成的伪数据转换为DataFrame
        df = pd.DataFrame(generated_data)
        output_file = "agnews_quick_pseudo_data.csv"
        df.to_csv(output_file, index=False)
        logging.info(f"Generated {len(df)} pseudo samples and saved to {output_file}")

        print("\n--- Generated Pseudo Data (First 5 samples) ---")
        print(df.head())

    except Exception as e:
        logging.error(f"An error occurred during quick pseudo-data generation: {e}", exc_info=True)
        print("Please check the logs for more details on the error.")

if __name__ == "__main__":
    quick_generate_pseudo_data(num_samples=10) # 尝试生成10个样本
```
```

**运行说明：**

1.  将上述代码保存为 `quick_start_generator.py`。
2.  在激活的Conda环境中运行 `python quick_start_generator.py`。
3.  程序将下载 `gpt2` 模型（如果尚未下载），然后生成伪数据并保存到 `agnews_quick_pseudo_data.csv` 文件中。

这个快速示例展示了生成器的基本工作流程：加载模型，构建提示，生成文本，并保存结果。请注意，`gpt2`模型较小，生成的文本质量可能有限。在后续章节中，我们将探讨如何提升生成质量和处理更复杂的场景。

### 4. 核心API详解

本节将深入探讨构建AG News伪数据生成器所涉及的核心API，主要包括Hugging Face `datasets`库用于数据加载，以及`transformers`库用于文本生成。

#### 4.1 AG News数据集加载：`datasets`库

`datasets`库提供了便捷的方式来加载各种NLP数据集。

*   **`load_dataset(name, split)`**:
    *   `name`: 数据集名称，对于AG News是 `'ag_news'`。
    *   `split`: 要加载的数据集分割，如 `'train'`（训练集）、`'test'`（测试集）。

**示例：加载AG News训练集**

```python
from datasets import load_dataset
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

try:
    # 加载AG News训练集
    logging.info("Loading AG News dataset (train split)...")
    agnews_dataset = load_dataset('ag_news', split='train')
    logging.info(f"Dataset loaded. Number of samples: {len(agnews_dataset)}")
    logging.info(f"Features: {agnews_dataset.features}")
    logging.info(f"First sample: {agnews_dataset[0]}")

    # 类别映射
    label_names = agnews_dataset.features['label'].names
    logging.info(f"AG News label names: {label_names}")

except Exception as e:
    logging.error(f"Failed to load AG News dataset: {e}", exc_info=True)

```

*   **数据集结构**: 加载后，`agnews_dataset` 是一个`Dataset`对象，行为类似于Python列表。每个元素是一个字典，包含`'text'`（新闻标题和描述合并）、`'label'`（整数ID）和可能的`'title'`、`'description'`等字段。`features`属性提供了数据集的元数据，包括标签名称。

#### 4.2 文本生成：`transformers`库

`transformers`库是文本生成的核心。它提供了`pipeline`高级接口，以及更底层的`AutoModelForCausalLM`和`AutoTokenizer`。

##### 4.2.1 `pipeline` 接口 (高级)

`pipeline`是Hugging Face `transformers`库提供的一个高级抽象，可以非常方便地进行各种NLP任务，包括文本生成。

*   **`transformers.pipeline(task, model, tokenizer, device)`**:
    *   `task`: 任务类型，对于文本生成是 `'text-generation'`。
    *   `model`: 预训练模型的名称（如 `'gpt2'`）或已加载的模型实例。
    *   `tokenizer`: 预训练分词器的名称或已加载的分词器实例。通常与`model`相同。
    *   `device`: 指定运行设备。`0`表示第一个GPU，`-1`表示CPU。

*   **生成方法调用**: `generator(prompt, **generation_kwargs)`
    *   `prompt`: 用于引导模型生成文本的输入字符串。
    *   `generation_kwargs`: 控制生成行为的参数，如`max_new_tokens`、`do_sample`、`temperature`等。

**常用生成参数详解：**

*   `max_new_tokens` (int): 生成文本的最大长度（不包括提示）。
*   `num_return_sequences` (int): 为每个提示生成的独立序列数量。
*   `do_sample` (bool): 如果为`True`，则使用采样策略（如温度采样、Top-K/Top-P采样），生成更具创造性和多样性的文本。如果为`False`，则使用贪婪解码或束搜索，生成确定性更强的文本。
*   `temperature` (float): 采样温度。值越高，生成文本越随机；值越低，生成文本越保守。通常在 0.7 到 1.0 之间。
*   `top_k` (int): Top-K 采样。在每一步，只考虑概率最高的K个词进行采样。限制了词汇选择的范围。
*   `top_p` (float): Top-P (核) 采样。在每一步，选择累积概率达到P的最小词汇集进行采样。也称为“nucleus sampling”。
*   `repetition_penalty` (float): 重复惩罚因子。大于1.0的值会惩罚重复的词语，避免模型陷入循环。
*   `pad_token_id` (int): 填充标记ID。某些模型需要指定，通常是`tokenizer.eos_token_id`或`tokenizer.pad_token_id`。
*   `eos_token_id` (int): 结束标记ID。模型生成该标记后停止。

**示例：使用`pipeline`进行文本生成**

```python
import torch
from transformers import pipeline, set_seed
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_device():
    """获取可用的计算设备 (GPU或CPU)。"""
    if torch.cuda.is_available():
        return "cuda"
    elif torch.backends.mps.is_available():
        return "mps"
    else:
        return "cpu"

def generate_text_with_pipeline(prompt: str, model_name: str = "gpt2", num_sequences: int = 1):
    """
    使用transformers pipeline生成文本。
    """
    device = get_device()
    set_seed(42) # 设置随机种子以保证结果可复现

    try:
        logging.info(f"Initializing generator with model: {model_name} on device: {device}")
        generator = pipeline(
            "text-generation",
            model=model_name,
            device=0 if device == "cuda" else -1 # 0 for first GPU, -1 for CPU
        )
        logging.info("Generator initialized.")

        logging.info(f"Generating {num_sequences} sequences with prompt: '{prompt}'")
        outputs = generator(
            prompt,
            max_new_tokens=60,       # 生成最长60个新词
            num_return_sequences=num_sequences,
            do_sample=True,          # 启用采样
            temperature=0.8,         # 采样温度
            top_k=50,                # Top-K 采样
            top_p=0.95,              # Top-P 采样
            repetition_penalty=1.2,  # 重复惩罚
            # 确保模型在生成结束时停止，否则可能生成无限长
            pad_token_id=generator.tokenizer.eos_token_id
        )

        generated_texts = []
        for i, output in enumerate(outputs):
            full_text = output['generated_text']
            # 移除提示部分
            generated_content = full_text.replace(prompt, '').strip()
            logging.info(f"Generated text {i+1}: '{generated_content[:100]}...'")
            generated_texts.append(generated_content)
        return generated_texts

    except Exception as e:
        logging.error(f"An error occurred during text generation: {e}", exc_info=True)
        return []

if __name__ == "__main__":
    test_prompt = "Generate a short news headline and description about a new scientific discovery:"
    generated = generate_text_with_pipeline(test_prompt, num_sequences=2)
    print("\n--- Generated Texts ---")
    for i, text in enumerate(generated):
        print(f"Sample {i+1}:\n{text}\n")
```
```

##### 4.2.2 `AutoModelForCausalLM` 和 `AutoTokenizer` (底层)

对于更精细的控制，或者当`pipeline`无法满足需求时（例如，需要自定义模型加载、多GPU并行等），可以直接使用`AutoModelForCausalLM`和`AutoTokenizer`。

*   **`AutoTokenizer.from_pretrained(model_name)`**: 加载预训练模型的分词器。分词器负责将文本转换为模型可以理解的数字ID。
*   **`AutoModelForCausalLM.from_pretrained(model_name)`**: 加载预训练的因果语言模型。因果语言模型通常用于文本生成，因为它预测序列中的下一个词。
*   **`tokenizer.encode(prompt, return_tensors='pt')`**: 将提示文本编码为PyTorch张量。
*   **`model.generate(input_ids, **generation_kwargs)`**: 使用模型生成文本。`input_ids`是编码后的提示张量。

**示例：使用底层API进行文本生成**

```python
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer, set_seed
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_device():
    """获取可用的计算设备 (GPU或CPU)。"""
    if torch.cuda.is_available():
        return "cuda"
    elif torch.backends.mps.is_available():
        return "mps"
    else:
        return "cpu"

def generate_text_with_low_level_api(prompt: str, model_name: str = "gpt2", num_sequences: int = 1):
    """
    使用transformers底层API生成文本。
    """
    device = get_device()
    set_seed(42)

    try:
        logging.info(f"Loading tokenizer and model: {model_name} on device: {device}")
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        model = AutoModelForCausalLM.from_pretrained(model_name)
        model.to(device)
        model.eval() # 设置为评估模式

        # 确保分词器有pad_token，某些模型可能没有
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
            logging.warning(f"Tokenizer for {model_name} does not have a pad_token. Setting it to eos_token: {tokenizer.pad_token}")

        input_ids = tokenizer.encode(prompt, return_tensors='pt').to(device)
        logging.info(f"Input IDs shape: {input_ids.shape}")

        logging.info(f"Generating {num_sequences} sequences with prompt: '{prompt}'")
        output_sequences = model.generate(
            input_ids=input_ids,
            max_new_tokens=60,
            num_return_sequences=num_sequences,
            do_sample=True,
            temperature=0.8,
            top_k=50,
            top_p=0.95,
            repetition_penalty=1.2,
            pad_token_id=tokenizer.pad_token_id,
            eos_token_id=tokenizer.eos_token_id # 确保模型在生成结束时停止
        )

        generated_texts = []
        for i, seq in enumerate(output_sequences):
            decoded_text = tokenizer.decode(seq, skip_special_tokens=True)
            # 移除提示部分
            generated_content = decoded_text.replace(prompt, '').strip()
            logging.info(f"Generated text {i+1}: '{generated_content[:100]}...'")
            generated_texts.append(generated_content)
        return generated_texts

    except Exception as e:
        logging.error(f"An error occurred during low-level text generation: {e}", exc_info=True)
        return []

if __name__ == "__main__":
    test_prompt = "Generate a short news headline and description about a new scientific discovery:"
    generated = generate_text_with_low_level_api(test_prompt, num_sequences=2)
    print("\n--- Generated Texts ---")
    for i, text in enumerate(generated):
        print(f"Sample {i+1}:\n{text}\n")
```
```

### 5. 实用代码示例

本节将提供一个更完整的AG News伪数据生成器，它将结合前述概念，实现按类别生成伪数据，并进行基本的数据清洗和保存。

我们将实现以下功能：
1.  加载AG News数据集，获取类别信息。
2.  为每个类别构建特定的生成提示。
3.  使用一个预训练语言模型（例如`distilgpt2`，比`gpt2`更小巧高效）批量生成伪数据。
4.  对生成的文本进行初步清洗，例如移除提示、处理多余空白字符。
5.  将生成的伪数据保存为CSV文件。

```python
import torch
from transformers import pipeline, set_seed
from datasets import load_dataset
import pandas as pd
import logging
import os
import re
from typing import List, Dict, Union

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_device():
    """获取可用的计算设备 (GPU或CPU)。"""
    if torch.cuda.is_available():
        return "cuda"
    elif torch.backends.mps.is_available(): # For Apple Silicon Macs
        return "mps"
    else:
        return "cpu"

class AGNewsPseudoDataGenerator:
    def __init__(self, model_name: str = "distilgpt2", device: str = None):
        """
        初始化AG News伪数据生成器。
        :param model_name: 用于文本生成的模型名称。
        :param device: 指定运行设备，'cuda', 'mps' 或 'cpu'。如果为None，则自动检测。
        """
        self.device = device if device else get_device()
        self.model_name = model_name
        self.generator = None
        self.agnews_labels = None
        set_seed(42) # 设置随机种子以保证结果可复现

        self._load_agnews_info()
        self._initialize_generator()

    def _load_agnews_info(self):
        """加载AG News数据集信息，获取类别标签。"""
        try:
            logging.info("Loading AG News dataset info to get labels...")
            # 只加载一小部分或直接从元数据获取，避免加载整个数据集
            dummy_dataset = load_dataset('ag_news', split='train[:1]')
            self.agnews_labels = dummy_dataset.features['label'].names
            logging.info(f"AG News categories: {self.agnews_labels}")
        except Exception as e:
            logging.error(f"Failed to load AG News dataset info: {e}", exc_info=True)
            # 提供默认标签以防加载失败
            self.agnews_labels = ["World", "Sports", "Business", "Sci/Tech"]
            logging.warning("Using default AG News categories due to load failure.")

    def _initialize_generator(self):
        """初始化文本生成pipeline。"""
        try:
            logging.info(f"Initializing text generation pipeline with model: {self.model_name} on device: {self.device}")
            self.generator = pipeline(
                "text-generation",
                model=self.model_name,
                device=0 if self.device == "cuda" else -1 # 0 for first GPU, -1 for CPU
            )
            logging.info("Text generation pipeline initialized successfully.")
            # 确保分词器有pad_token，某些模型可能没有
            if self.generator.tokenizer.pad_token is None:
                self.generator.tokenizer.pad_token = self.generator.tokenizer.eos_token
                logging.warning(f"Tokenizer for {self.model_name} does not have a pad_token. Setting it to eos_token: {self.generator.tokenizer.pad_token}")

        except Exception as e:
            logging.error(f"Failed to initialize text generation pipeline: {e}", exc_info=True)
            raise RuntimeError("Generator initialization failed. Check model name or device setup.")

    def _clean_generated_text(self, raw_text: str, prompt: str) -> Dict[str, str]:
        """
        清洗生成的原始文本，尝试提取标题和描述。
        :param raw_text: 模型生成的原始文本。
        :param prompt: 用于生成的原始提示。
        :return: 包含 'headline' 和 'description' 的字典。
        """
        cleaned_text = raw_text.replace(prompt, '').strip()

        # 尝试通过换行符或常见标点符号分割标题和描述
        # 假设标题通常在第一行，或以句号、问号、感叹号结尾
        match = re.match(r"^(.*?)(?:\n|\.|\?|!)(.*)$", cleaned_text, re.DOTALL)
        if match:
            headline = match.group(1).strip()
            description = match.group(2).strip()
        else:
            # 如果无法明确分割，将整个文本作为描述，或尝试分割前20%作为标题
            if len(cleaned_text) > 50: # 简单启发式
                split_point = cleaned_text.find('.', min(20, len(cleaned_text) // 4)) # 尝试在文本前部找句号
                if split_point != -1:
                    headline = cleaned_text[:split_point+1].strip()
                    description = cleaned_text[split_point+1:].strip()
                else:
                    headline = cleaned_text.split(' ', 1)[0] + "..." if ' ' in cleaned_text else cleaned_text
                    description = cleaned_text
            else:
                headline = cleaned_text
                description = ""

        # 进一步清理，移除多余的空白行或特殊字符
        headline = re.sub(r'\s+', ' ', headline).strip()
        description = re.sub(r'\s+', ' ', description).strip()

        return {"headline": headline, "description": description}

    def generate_pseudo_samples(self,
                                num_samples_per_category: int = 10,
                                max_new_tokens: int = 100,
                                temperature: float = 0.8,
                                top_k: int = 50,
                                top_p: float = 0.95,
                                repetition_penalty: float = 1.2
                                ) -> pd.DataFrame:
        """
        为每个AG News类别生成伪数据。
        :param num_samples_per_category: 每个类别要生成的样本数量。
        :param max_new_tokens: 生成文本的最大长度。
        :param temperature: 采样温度。
        :param top_k: Top-K 采样参数。
        :param top_p: Top-P 采样参数。
        :param repetition_penalty: 重复惩罚因子。
        :return: 包含所有生成伪数据的Pandas DataFrame。
        """
        if not self.generator or not self.agnews_labels:
            logging.error("Generator or AG News labels not initialized. Cannot generate data.")
            return pd.DataFrame()

        all_generated_data = []

        for label_id, category_name in enumerate(self.agnews_labels):
            logging.info(f"Generating {num_samples_per_category} samples for category: '{category_name}' (ID: {label_id})")
            # 构建生成提示
            # 提示设计对生成质量至关重要，可以根据需要进行调整
            prompt = f"Write a news headline and a brief news article description about {category_name}:"

            try:
                outputs = self.generator(
                    prompt,
                    max_new_tokens=max_new_tokens,
                    num_return_sequences=num_samples_per_category,
                    do_sample=True,
                    temperature=temperature,
                    top_k=top_k,
                    top_p=top_p,
                    repetition_penalty=repetition_penalty,
                    pad_token_id=self.generator.tokenizer.eos_token_id,
                    eos_token_id=self.generator.tokenizer.eos_token_id
                )

                for i, output in enumerate(outputs):
                    raw_generated_text = output['generated_text']
                    cleaned_parts = self._clean_generated_text(raw_generated_text, prompt)

                    all_generated_data.append({
                        "id": len(all_generated_data),
                        "label": label_id,
                        "label_name": category_name,
                        "title": cleaned_parts["headline"],
                        "description": cleaned_parts["description"],
                        "text": cleaned_parts["headline"] + " " + cleaned_parts["description"], # 模拟AG News的'text'字段
                        "source_prompt": prompt,
                        "raw_generated_text": raw_generated_text
                    })
                    if (i + 1) % 10 == 0 or (i + 1) == num_samples_per_category:
                        logging.info(f"  Generated {i+1}/{num_samples_per_category} for {category_name}")

            except Exception as e:
                logging.error(f"Error generating for category '{category_name}': {e}", exc_info=True)
                continue # 继续下一个类别

        df = pd.DataFrame(all_generated_data)
        logging.info(f"Successfully generated a total of {len(df)} pseudo samples.")
        return df

    def save_pseudo_data(self, df: pd.DataFrame, output_path: str = "agnews_pseudo_data.csv"):
        """
        将生成的伪数据保存到CSV文件。
        :param df: 包含伪数据的DataFrame。
        :param output_path: 输出文件路径。
        """
        try:
            df.to_csv(output_path, index=False)
            logging.info(f"Pseudo data saved successfully to {output_path}")
        except Exception as e:
            logging.error(f"Failed to save pseudo data to {output_path}: {e}", exc_info=True)

if __name__ == "__main__":
    # 实例化生成器
    # 可以尝试使用更大的模型，但需要更多内存和计算资源，例如 "gpt2-medium", "gpt2-large"
    generator = AGNewsPseudoDataGenerator(model_name="distilgpt2")

    # 生成伪数据
    # 为每个类别生成20个样本，总共80个样本
    pseudo_df = generator.generate_pseudo_samples(num_samples_per_category=20, max_new_tokens=150)

    # 保存伪数据
    output_filename = "agnews_generated_pseudo_data.csv"
    generator.save_pseudo_data(pseudo_df, output_filename)

    print("\n--- First 5 Generated Pseudo Data Samples ---")
    print(pseudo_df[['label_name', 'title', 'description']].head())

    print(f"\nTotal generated samples: {len(pseudo_df)}")
    print(f"Pseudo data saved to: {output_filename}")
```
```

**代码说明：**

*   **`AGNewsPseudoDataGenerator` 类**: 封装了生成器的所有逻辑，包括模型加载、类别信息获取、文本清洗和数据保存。
*   **`_load_agnews_info()`**: 从`datasets`库加载AG News的元数据，获取类别名称。
*   **`_initialize_generator()`**: 初始化`transformers`的`pipeline`对象。
*   **`_clean_generated_text()`**: 这是一个关键的后处理步骤。LLM生成的文本通常包含提示，且可能格式不规整。此函数尝试：
    *   移除原始提示。
    *   根据换行符或常见标点符号（`.`, `?`, `!`）来分割标题和描述。
    *   处理多余的空格。
    *   **重要提示**: 这个清洗逻辑是启发式的，对于不同模型和生成参数，可能需要更复杂的正则表达式或NLP解析来精确提取标题和描述。
*   **`generate_pseudo_samples()`**: 这是核心生成方法。它遍历每个AG News类别，为每个类别构建一个有针对性的提示，然后调用`self.generator`进行批量文本生成。
*   **`save_pseudo_data()`**: 将生成的DataFrame保存为CSV文件。

**如何使用：**

1.  将代码保存为 `agnews_generator.py`。
2.  在您的Python环境中运行 `python agnews_generator.py`。
3.  程序将开始生成伪数据，并在完成后保存到 `agnews_generated_pseudo_data.csv`。

### 6. 高级功能与配置

本节将介绍如何通过调整模型、生成参数和采用更复杂的提示策略来提升伪数据的质量和多样性。

#### 6.1 高级LLM模型选择

选择合适的模型对生成质量至关重要。`gpt2`和`distilgpt2`是小模型，适合快速测试。对于生产级别的伪数据生成，您可能需要更大的模型。

*   **更大的GPT系列模型**: `gpt2-medium`, `gpt2-large`, `gpt2-xl`。这些模型拥有更多参数，能够生成更连贯、更符合语境的文本，但需要更多的GPU内存和计算资源。
*   **特定领域的微调模型**: 如果有针对新闻领域进行过微调的模型，它们会比通用模型生成更高质量的新闻文本。
*   **开源的SOTA模型**: 例如Llama 2、Mistral、Falcon等。这些模型通常需要更大的资源（特别是7B以上版本），但其生成能力远超GPT-2系列。您可能需要安装`bitsandbytes`库以支持量化加载，从而减少内存占用。

**示例：使用GPT-2 Large模型（需更多内存）**

```python
# 在 AGNewsPseudoDataGenerator 初始化时
# generator = AGNewsPseudoDataGenerator(model_name="gpt2-large")
# 请确保您的GPU有足够的显存 (例如，gpt2-large 需要约3GB显存)
```

**示例：加载量化模型（以Mistral-7B为例，需安装bitsandbytes）**

```python
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer, BitsAndBytesConfig
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_device():
    if torch.cuda.is_available():
        return "cuda"
    elif torch.backends.mps.is_available():
        return "mps"
    else:
        return "cpu"

def load_quantized_model(model_name: str = "mistralai/Mistral-7B-Instruct-v0.2"):
    """
    加载4位量化的模型，以减少内存占用。
    需要安装 'bitsandbytes' 和 'accelerate' 库。
    """
    device = get_device()
    if device != "cuda":
        logging.warning("Quantization is primarily for GPU. Loading full model on CPU/MPS.")
        # 如果不是CUDA设备，则不进行量化加载
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        model = AutoModelForCausalLM.from_pretrained(model_name)
        model.to(device)
        return tokenizer, model

    try:
        logging.info(f"Attempting to load quantized model: {model_name} on device: {device}")
        bnb_config = BitsAndBytesConfig(
            load_in_4bit=True,
            bnb_4bit_quant_type="nf4",
            bnb_4bit_compute_dtype=torch.bfloat16,
            bnb_4bit_use_double_quant=True,
        )

        tokenizer = AutoTokenizer.from_pretrained(model_name)
        model = AutoModelForCausalLM.from_pretrained(
            model_name,
            quantization_config=bnb_config,
            device_map="auto" # 自动分配到可用设备
        )
        logging.info(f"Quantized model {model_name} loaded successfully.")
        return tokenizer, model

    except ImportError:
        logging.error("bitsandbytes or accelerate not installed. Cannot load quantized model. Please install them: pip install bitsandbytes accelerate")
        raise
    except Exception as e:
        logging.error(f"Failed to load quantized model {model_name}: {e}", exc_info=True)
        raise

if __name__ == "__main__":
    # 这是一个示例，实际运行可能需要更多步骤来集成到生成器类中
    # 并且Mistral模型通常需要特定的prompt格式 (如 Instruct 格式)
    try:
        # 注意: Mistral-7B-Instruct-v0.2 是一个大型模型，即使量化也可能需要8GB+ GPU显存
        # 且其生成方式通常是对话式的，需要更复杂的prompt构造
        tokenizer, model = load_quantized_model()
        logging.info(f"Model and tokenizer loaded. Model device: {model.device}")

        # 示例生成 (对于Instruct模型，需要遵循其指令格式)
        # prompt = "<s>[INST] Generate a news headline and a short description about sports. [/INST]"
        # input_ids = tokenizer.encode(prompt, return_tensors='pt').to(model.device)
        # generated_ids = model.generate(input_ids, max_new_tokens=100, num_return_sequences=1, do_sample=True, temperature=0.7)
        # generated_text = tokenizer.decode(generated_ids[0], skip_special_tokens=True)
        # logging.info(f"Generated text: {generated_text}")

    except Exception as e:
        print(f"Failed to load model for demonstration: {e}")
```
```

#### 6.2 优化生成参数

精确调整生成参数可以显著影响生成文本的质量和多样性。

*   **`temperature`**:
    *   `0.7 - 0.9`: 常用范围，兼顾随机性和连贯性。
    *   `> 1.0`: 增加随机性，可能生成更有创意但连贯性较差的文本。
    *   `< 0.7`: 降低随机性，生成更保守、更重复的文本。
*   **`top_k` 和 `top_p`**:
    *   **推荐组合使用**：通常同时设置`top_k`和`top_p`，模型会先应用`top_k`，再在剩余的词汇上应用`top_p`。
    *   `top_k=0` 或 `top_p=1.0`: 禁用该策略。
    *   `top_k=50`, `top_p=0.95`: 常见设置，在保持多样性的同时避免生成完全不相关的词。
*   **`repetition_penalty`**:
    *   `1.0`: 无惩罚。
    *   `1.0 - 1.2`: 轻微惩罚，减少重复。
    *   `> 1.2`: 强烈惩罚，可能导致文本缺乏连贯性或难以生成特定短语。

#### 6.3 复杂的提示工程（Prompt Engineering）

提示工程是引导LLM生成高质量伪数据的关键。

*   **上下文示例**：在提示中加入AG News真实样本作为上下文，引导模型生成风格相似的文本。
    ```python
    # 假设我们从AG News数据集中取了几个真实示例
    real_examples = [
        {"title": "Oil Prices Surge on Middle East Tensions", "description": "Global oil prices jumped on Monday as escalating tensions in the Middle East raised supply concerns."},
        {"title": "Tech Giants Invest Heavily in AI Research", "description": "Leading technology companies are pouring billions into artificial intelligence research, signaling a new era of innovation."}
    ]
    # 构建更复杂的提示
    prompt_with_examples = (
        f"Here are some examples of news articles:\n"
        f"1. Title: {real_examples[0]['title']}\nDescription: {real_examples[0]['description']}\n"
        f"2. Title: {real_examples[1]['title']}\nDescription: {real_examples[1]['description']}\n\n"
        f"Now, generate a news headline and a brief news article description about {category_name}, following a similar style:"
    )
    ```
*   **角色扮演**：让模型扮演新闻记者或编辑。
    ```python
    prompt_role_play = f"As a seasoned news reporter, write a compelling news headline and a brief description for a story about {category_name}:"
    ```
*   **指定格式**：明确要求输出的格式，例如“Headline: \[...] Description: \[[...]”
    ```python
    prompt_format = f"Generate a news article about {category_name} in the following format:\nHeadline: \nDescription: \n"
    ```
    在`_clean_generated_text`中，您可能需要使用更复杂的正则表达式来匹配这些特定格式。

#### 6.4 批量处理与并行生成

对于生成大量伪数据，批量处理和并行化是提高效率的关键。

*   **`pipeline`的批量输入**: `pipeline`本身支持传递一个提示列表，它会自动进行批量推理。
    ```python
    # prompts = [f"Generate news about {category}" for _ in range(num_samples)]
    # outputs = generator(prompts, ...) # 这将自动批量处理
    ```
*   **多GPU并行**: `device_map="auto"`参数可以在多个GPU之间自动分配模型层。对于大型模型，这可以显著提升性能。
    ```python
    from transformers import AutoModelForCausalLM, AutoTokenizer
    # model = AutoModelForCausalLM.from_pretrained(model_name, device_map="auto")
    # tokenizer = AutoTokenizer.from_pretrained(model_name)
    # generator = pipeline("text-generation", model=model, tokenizer=tokenizer) # pipeline会自动使用device_map
    ```
*   **多进程/多线程**: 对于CPU推理或多个小型模型，可以使用Python的`multiprocessing`或`threading`库并行运行多个生成器实例。这在GPU资源有限时尤其有用。

```python
# 示例：批量生成伪数据（已在实用代码示例中演示，此为更明确的说明）
# 在 generate_pseudo_samples 方法中，generator(prompt, num_return_sequences=num_samples_per_category)
# 实际上已经在内部进行了批量生成，因为num_return_sequences参数会一次性生成多个序列。
# 如果你想同时为多个不同的prompt生成，可以传入一个prompt列表给pipeline
# prompts_for_batch = [f"Generate news about {category_name}" for _ in range(num_samples_per_category)]
# outputs = self.generator(prompts_for_batch, ...) # 这样会为每个prompt生成一个序列
```

#### 6.5 生成文本的后处理与过滤

生成的文本并非总是完美的。需要进行额外的后处理步骤：

*   **去重**: 移除重复或高度相似的伪数据。
*   **质量过滤**:
    *   **长度过滤**: 移除过短或过长的文本。
    *   **关键词过滤**: 确保生成的文本包含相关关键词，或不包含不希望出现的关键词。
    *   **语法检查/流畅度评估**: 使用语言模型或规则来评估文本的语法正确性和可读性。
    *   **主题一致性检查**: 确保生成的文本确实属于目标类别。可以使用一个预训练的分类器对生成的文本进行分类，只保留那些被正确分类的样本。
*   **格式规范化**: 确保标题和描述的格式一致。

```python
# 示例：去重和简单质量过滤
import pandas as pd
import logging

def filter_and_deduplicate_pseudo_data(df: pd.DataFrame) -> pd.DataFrame:
    """
    对生成的伪数据进行去重和简单质量过滤。
    :param df: 包含伪数据的DataFrame。
    :return: 过滤和去重后的DataFrame。
    """
    initial_count = len(df)
    logging.info(f"Initial pseudo data count: {initial_count}")

    # 1. 基于 'text' 字段去重
    df_deduplicated = df.drop_duplicates(subset=['text'], keep='first')
    logging.info(f"After deduplication: {len(df_deduplicated)} samples (removed {initial_count - len(df_deduplicated)})")

    # 2. 简单质量过滤：移除过短的文本
    # 假设标题至少5个词，描述至少10个词
    df_filtered_length = df_deduplicated[
        (df_deduplicated['title'].apply(lambda x: len(x.split())) >= 3) &
        (df_deduplicated['description'].apply(lambda x: len(x.split())) >= 5)
    ]
    logging.info(f"After length filtering: {len(df_filtered_length)} samples (removed {len(df_deduplicated) - len(df_filtered_length)})")

    # 3. 更高级的过滤（例如，使用分类器验证类别一致性）
    # 这需要加载一个AG News分类模型，并对每个生成的样本进行预测
    # 如果预测类别与生成时的目标类别不符，则移除
    # 伪代码示例：
    # from transformers import AutoModelForSequenceClassification, AutoTokenizer
    # classifier_tokenizer = AutoTokenizer.from_pretrained("path/to/agnews_classifier_model")
    # classifier_model = AutoModelForSequenceClassification.from_pretrained("path/to/agnews_classifier_model")
    # classifier_pipeline = pipeline("text-classification", model=classifier_model, tokenizer=classifier_tokenizer, device=get_device())

    # def verify_category(row):
    #     pred = classifier_pipeline(row['text'])[0]
    #     predicted_label_id = agnews_labels.index(pred['label']) # 假设agnews_labels已定义
    #     return predicted_label_id == row['label']

    # df_verified = df_filtered_length[df_filtered_length.apply(verify_category, axis=1)]
    # logging.info(f"After category verification: {len(df_verified)} samples (removed {len(df_filtered_length) - len(df_verified)})")

    return df_filtered_length

if __name__ == "__main__":
    # 假设 pseudo_df 是通过之前的生成器生成的DataFrame
    # pseudo_df = generator.generate_pseudo_samples(...)
    # 为了演示，创建一个示例DataFrame
    sample_data = {
        'id': [0, 1, 2, 3, 4, 5],
        'label': [0, 0, 1, 1, 2, 2],
        'label_name': ['World', 'World', 'Sports', 'Sports', 'Business', 'Business'],
        'title': ['Global Leaders Meet on Climate', 'World', 'Team Wins Championship', 'Sports News', 'Market Sees Gains', 'Business Talk'],
        'description': ['Leaders from around the world gathered to discuss climate change policies and their impact.', 'This is a short text.', 'The local team celebrated after winning the national championship in a thrilling final.', 'A very short description.', 'The stock market experienced significant gains today, driven by strong tech sector performance.', 'Another short desc.']
    }
    pseudo_df_example = pd.DataFrame(sample_data)

    # 增加一个重复项
    pseudo_df_example = pd.concat([pseudo_df_example, pd.DataFrame([sample_data['id'][0], sample_data['label'][0], sample_data['label_name'][0], sample_data['title'][0], sample_data['description'][0]], index=pseudo_df_example.columns).T], ignore_index=True)
    pseudo_df_example['text'] = pseudo_df_example['title'] + " " + pseudo_df_example['description']

    print("--- Before Filtering ---")
    print(pseudo_df_example[['label_name', 'title', 'description']])
    print(f"Total: {len(pseudo_df_example)}")

    filtered_df = filter_and_deduplicate_pseudo_data(pseudo_df_example)

    print("\n--- After Filtering ---")
    print(filtered_df[['label_name', 'title', 'description']])
    print(f"Total: {len(filtered_df)}")
```
```

### 7. 性能优化技巧

生成大量高质量的伪数据可能是一个计算密集型任务。以下是一些性能优化技巧：

1.  **利用GPU加速**:
    *   **确保PyTorch和Transformers使用CUDA**: 这是最重要的优化。在`AGNewsPseudoDataGenerator`的`_initialize_generator`和`get_device`中已实现。
    *   **`device_map="auto"`**: 对于大型模型，可以在加载模型时使用`device_map="auto"`，让Hugging Face自动将模型层分配到可用的GPU上，甚至跨多个GPU。
    ```python
    # 在_initialize_generator中
    # self.generator = pipeline(
    #     "text-generation",
    #     model=self.model_name,
    #     torch_dtype=torch.float16, # 使用FP16减少显存占用和加速计算
    #     device_map="auto" # 自动分配设备
    # )
    ```
    *   **`torch_dtype=torch.float16` 或 `torch.bfloat16`**: 使用半精度浮点数（FP16或BF16）可以显著减少模型在GPU上的显存占用，并加速推理。大多数现代NVIDIA GPU都支持FP16。
        *   需要安装`accelerate`库。
        *   如果使用`bfloat16`，需要更新的GPU（如NVIDIA Ampere架构及更高版本）。
2.  **模型量化 (Quantization)**:
    *   将模型权重从32位浮点数转换为更低的精度（如8位或4位整数），可以大幅减少内存占用和提高推理速度。
    *   Hugging Face `transformers`与`bitsandbytes`库集成，支持4位量

---

*注：由于内容长度限制，部分内容可能被截断。建议查阅官方文档获取完整信息。*

## References

[REF_1] **Source 1**  
   🔗 https://www.ttdm3.me/vod/16712/

[REF_2] **Source 2**  
   🔗 https://www.samsung.com.cn/support/mobile-devices/what-to-do-when-calls-disconnect-or-there-is-no-network-signal-on-your-galaxy-device/

[REF_3] **Modern ZZMI  - A Tutorial for Zenless Zone Zero.**  
   🔗 https://GameBanana.com/tuts/18915

[REF_4] **Source 4**  
   🔗 https://1anime2025.me/vodplay/8208-1-1.html

[REF_5] **Source 5**  
   🔗 https://www.galgamezywz.com/game/8879

[REF_6] **Source 6**  
   🔗 https://www.sonystyle.com.cn/products/headphone/wh_1000xm6/wh_1000xm6_s_fgo_tzgj.html

[REF_7] **Source 7**  
   🔗 https://clashnode.cc/free-node/2025-7-21-free-ssr-subscribe.htm

[REF_8] **Source 8**  
   🔗 https://www.bbc.com/zhongwen/articles/cly8e40ekmyo/simp

[REF_9] **Source 9**  
   🔗 https://live.warthunder.com/post/1146094/en/

[REF_10] **Source 10**  
   🔗 https://chengleegame.com/【安卓pc-精品slg】失调-dissonance-v3-汉化版-汉化-动态-【双端共3/

[REF_11] **Source 11**  
   🔗 https://www.ahhhhfs.com/73816/

[REF_12] **Snapmark Server**  
   🔗 https://marketplace.visualstudio.com/items?itemName=maxwell-ai-dev.snapmark-server

[REF_13] **Navigation Menu**  
   🔗 https://github.com/bradjbailey/572-project/blob/main/pytorch-agnews-tutorial-copy.py

---

*Report generated by DeepResearch*
