import litellm
from deep_research.config.settings import DEFAULT_LLM_PROVIDER
from deep_research.config.prompts import POPULAR_SCIENCE_PROMPT, ACADEMIC_PROMPT, ENGINEERING_PROMPT
import re

class SynthesisAgent:
    def __init__(self, model_override=None):
        self.model = model_override or DEFAULT_LLM_PROVIDER

    def synthesize(self, topic: str, mode: str, contexts: dict[str, str], language: str = 'auto') -> str:
        """综合分析内容并生成报告"""
        if not contexts:
            return ""
        
        output_language = self._determine_language(topic, language)
        print(f"🤖 [Phase 3/3] Generating {mode} report in {output_language}...")
        
        try:
            # 使用单次调用生成完整报告
            prompt = self._build_comprehensive_prompt(topic, mode, contexts, output_language)
            
            model_params = self._get_model_params(self.model)
            response = litellm.completion(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                **model_params
            )
            
            content = response.choices[0].message.content

            # 检查内容是否为空
            if not content:
                print("❌ LLM returned empty content")
                return ""

            # 确保内容完整性
            if mode == 'engineering':
                content = self._ensure_content_completeness(content, topic)

            # 确保包含参考文献
            content = self._ensure_complete_references(content, contexts)
            
            return content
            
        except Exception as e:
            print(f"❌ Error generating report: {e}")
            return ""

    def _build_comprehensive_prompt(self, topic: str, mode: str, contexts: dict[str, str], output_language: str) -> str:
        """构建综合提示"""
        base_prompt = self._get_prompt(mode)
        context_text = self._build_context(contexts)
        
        return base_prompt.format(
            topic=topic,
            language=output_language,
            context=context_text  # 修复参数名
        )

    def _ensure_content_completeness(self, content: str, topic: str) -> str:
        """确保技术文档内容完整性"""
        # 安全检查：确保content不为None或空
        if not content:
            return content or ""

        # 检查是否有未闭合的代码块
        code_block_count = content.count('```')
        if code_block_count % 2 != 0:
            content += '\n```'
            print("⚠️  Fixed unclosed code block in generated content")
        
        # 检查是否内容被截断（以不完整的句子结尾）
        last_lines = content.strip().split('\n')[-5:]
        last_content = ' '.join(last_lines).strip()
        
        # 扩展截断检测模式
        truncation_patterns = [
            '...',
            ',',
            '，',
            '在内部会创建一个',
            '可以通过',
            '需要注意的是',
            '例如：',
            '如下所示：',
            '具体来说',
            '总的来说'
        ]
        
        is_truncated = False
        for pattern in truncation_patterns:
            if last_content.endswith(pattern):
                is_truncated = True
                break
        
        # 检查是否以完整的句子结尾
        if not is_truncated:
            valid_endings = ('.', '。', '!', '！', '?', '？', '```', ')', '）', '"', '"', ':', '：')
            if not last_content.endswith(valid_endings):
                is_truncated = True
        
        if is_truncated:
            print("⚠️  Content appears to be truncated, adding completion note")
            content += "\n\n---\n\n*注：由于内容长度限制，部分内容可能被截断。建议查阅官方文档获取完整信息。*"
        
        return content

    def _get_model_params(self, model: str) -> dict:
        """根据模型类型返回特定参数，优化输出长度"""
        params = {
            "temperature": 0.7,
            "max_tokens": 10000  # 增加默认输出长度
        }
        
        # Gemini 模型的特殊配置
        if model.startswith("gemini/"):
            params.update({
                "temperature": 0.7,
                "max_tokens": 15000,  # Gemini 支持更长的输出
            })
        
        # DeepSeek 模型的特殊配置
        elif model.startswith("deepseek/"):
            params.update({
                "temperature": 0.7,
                "max_tokens": 15000,  # 增加输出长度
            })
        
        # Claude 模型的特殊配置
        elif model.startswith("anthropic/"):
            params.update({
                "temperature": 0.7,
                "max_tokens": 15000,  # 增加输出长度
            })
        
        # OpenAI 模型配置
        elif model.startswith("openai/"):
            params.update({
                "temperature": 0.7,
                "max_tokens": 10000,  # OpenAI 的限制
            })
        
        return params

    def _get_prompt(self, mode: str) -> str:
        """根据模式获取对应的提示模板"""
        if mode == 'academic':
            return ACADEMIC_PROMPT
        elif mode == 'engineering':
            return ENGINEERING_PROMPT
        else:
            return POPULAR_SCIENCE_PROMPT

    def _build_context(self, contexts: dict[str, str]) -> str:
        """构建上下文文本"""
        context_parts = []
        for i, (url, content) in enumerate(contexts.items(), 1):
            context_parts.append(f"[REF_{i}] {url}\n{content[:2000]}...")
        return "\n\n".join(context_parts)

    def _determine_language(self, topic: str, language: str) -> str:
        """确定输出语言"""
        if language == 'auto':
            # 检测主题语言
            import re
            if re.search(r'[\u4e00-\u9fff]', topic):
                return '中文'
            else:
                return 'English'
        elif language == 'zh':
            return '中文'
        elif language == 'en':
            return 'English'
        else:
            return language

    def _ensure_complete_references(self, content: str, contexts: dict[str, str]) -> str:
        """确保包含完整的参考文献"""
        # 安全检查：确保content不为None或空
        if not content:
            return content or ""

        if "## References" not in content and "## 参考文献" not in content:
            references = self._generate_complete_references(contexts)
            content += f"\n\n## References\n\n{references}"
        return content

    def _generate_complete_references(self, contexts: dict[str, str]) -> str:
        """生成完整的参考文献列表"""
        references = []
        for i, (url, content) in enumerate(contexts.items(), 1):
            # 尝试从内容中提取标题
            title = self._extract_title_from_content(content) or f"Source {i}"
            references.append(f"[REF_{i}] **{title}**  \n   🔗 {url}")
        return "\n\n".join(references)

    def _extract_title_from_content(self, content: str) -> str:
        """从内容中提取标题"""
        lines = content.split('\n')
        for line in lines[:10]:  # 检查前10行
            line = line.strip()
            if line.startswith('# '):
                return line[2:].strip()
            elif line.startswith('## '):
                return line[3:].strip()
        return None






