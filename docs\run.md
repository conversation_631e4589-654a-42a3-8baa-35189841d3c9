Usage: main.py [OPTIONS] TOPIC

  Performs deep research on a given topic using Crawl4AI and DuckDuckGo.

  Examples: python main.py "人工智能" --mode popular --language zh python main.py    
  "Artificial Intelligence" --mode academic --language en python main.py
  "機械学習" --mode engineering --language auto

Options:
  --mode [popular|academic|engineering]
                                  Type of research to perform.
  --language, --lang [auto|zh|en|ja|ko|fr|de|es|ru]
                                  Output language (auto=detect from topic)
  --model TEXT                    Override the default LLM model
  --help                          Show this message and exit.



python main.py "http://push2.eastmoney.com/api 获取实时交易数据的详细使用方法 包括T+0etf涨速 板块涨速" --mode engineering --language zh
