"""工程技术文档格式化工具"""
import re
from typing import Dict, List

class EngineeringFormatter:
    """技术文档格式化助手"""
    
    @staticmethod
    def format_engineering_report(content: str, topic: str, contexts: Dict[str, str]) -> str:
        """格式化技术报告，确保符合工程标准"""
        
        # 检查内容是否已经包含头部信息，如果有则移除重复
        content = EngineeringFormatter._remove_duplicate_headers(content, topic)
        
        # 添加技术文档头部
        header = EngineeringFormatter._generate_tech_header(topic)
        
        # 确保代码示例的完整性
        formatted_content = EngineeringFormatter._enhance_code_examples(content)
        
        # 添加技术规范检查
        formatted_content = EngineeringFormatter._add_tech_standards(formatted_content)
        
        # 确保包含完整的参考文献
        if not EngineeringFormatter._has_complete_references(formatted_content, contexts):
            print("📝 Adding complete technical references...")
            formatted_content = EngineeringFormatter._remove_existing_references(formatted_content)
            references = EngineeringFormatter._generate_tech_references(contexts, formatted_content)
            formatted_content += f"\n\n{references}"
        
        # 组合完整的技术报告
        full_report = f"{header}\n\n{formatted_content}"
        
        return full_report
    
    @staticmethod
    def _generate_tech_header(topic: str) -> str:
        """生成技术文档头部"""
        from datetime import datetime
        current_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        return f"""# {topic} - Engineering Guide

**Generated:** {current_date}  
**Document Type:** Technical Implementation Guide  
**Target Audience:** Software Engineers & Developers  
**Complexity Level:** Beginner to Advanced  

---

## 📋 Document Overview

This comprehensive technical guide provides practical implementation details, code examples, and best practices for {topic}. All code examples are production-ready and include proper error handling.

### 🎯 What You'll Learn
- Core concepts and architecture
- Step-by-step implementation
- Advanced configuration options
- Performance optimization techniques
- Production deployment strategies
- Troubleshooting and debugging

### 📚 Prerequisites
- Basic programming knowledge
- Familiarity with relevant technologies
- Development environment setup

---"""
    
    @staticmethod
    def _enhance_code_examples(content: str) -> str:
        """增强代码示例的完整性，确保代码块正确闭合"""
        # 首先修复未闭合的代码块
        lines = content.split('\n')
        enhanced_lines = []
        in_code_block = False
        code_block_lang = None
        
        for line in lines:
            if line.strip().startswith('```'):
                if not in_code_block:
                    # 开始代码块
                    in_code_block = True
                    code_block_lang = line.strip()[3:].strip() or 'python'
                    enhanced_lines.append(line)
                else:
                    # 结束代码块
                    in_code_block = False
                    code_block_lang = None
                    enhanced_lines.append(line)
            else:
                enhanced_lines.append(line)
        
        # 如果最后还有未闭合的代码块，添加闭合标记
        if in_code_block:
            enhanced_lines.append('```')
            print("⚠️  Fixed unclosed code block at end of content")
        
        content = '\n'.join(enhanced_lines)
        
        # 查找代码块并增强
        code_pattern = r'```(\w+)?\n(.*?)\n```'
        
        def enhance_code_block(match):
            language = match.group(1) or 'python'
            code = match.group(2)
            
            # 确保代码不为空
            if not code.strip():
                return f'```{language}\n# Empty code block\n```'
            
            # 如果是Python代码且缺少导入，添加常见导入
            if language.lower() == 'python':
                imports_needed = []
                
                if 'asyncio' in code and 'import asyncio' not in code:
                    imports_needed.append('import asyncio')
                if 'AsyncWebCrawler' in code and 'from crawl4ai import' not in code:
                    imports_needed.append('from crawl4ai import AsyncWebCrawler')
                if 'CrawlerRunConfig' in code and 'CrawlerRunConfig' not in imports_needed:
                    imports_needed.append('from crawl4ai import CrawlerRunConfig')
                if 'logging' in code and 'import logging' not in code:
                    imports_needed.append('import logging')
                
                if imports_needed:
                    code = '\n'.join(imports_needed) + '\n\n' + code
                
                # 添加错误处理提示（如果代码较长且没有try-except）
                if 'try:' not in code and len(code.split('\n')) > 5:
                    code += "\n\n# Note: Add proper error handling in production"
            
            return f'```{language}\n{code}\n```'
        
        # 处理代码块
        enhanced_content = re.sub(code_pattern, enhance_code_block, content, flags=re.DOTALL)
        
        return enhanced_content
    
    @staticmethod
    def _add_tech_standards(content: str) -> str:
        """添加技术标准和最佳实践"""
        
        # 如果缺少性能优化部分，添加提示
        if "性能优化" not in content and "Performance" not in content:
            content += "\n\n## ⚡ 性能优化建议\n\n"
            content += "- 使用连接池减少网络开销\n"
            content += "- 实现适当的缓存策略\n"
            content += "- 监控内存使用情况\n"
            content += "- 设置合理的超时时间\n"
        
        # 如果缺少错误处理部分，添加提示
        if "错误处理" not in content and "Error Handling" not in content:
            content += "\n\n## 🚨 错误处理与调试\n\n"
            content += "### 常见错误类型\n"
            content += "- 网络连接超时\n"
            content += "- 内存不足\n"
            content += "- 权限问题\n"
            content += "- 依赖版本冲突\n\n"
            content += "### 调试技巧\n"
            content += "- 启用详细日志记录\n"
            content += "- 使用断点调试\n"
            content += "- 监控系统资源\n"
        
        return content
    
    @staticmethod
    def _has_complete_references(content: str, contexts: Dict[str, str]) -> bool:
        """检查是否有完整的技术参考文献"""
        has_refs = "## References" in content or "## 参考文献" in content
        if not has_refs:
            return False
        
        # 检查引用数量
        ref_count = len(re.findall(r'\[REF_\d+\]', content))
        return ref_count >= len(contexts)
    
    @staticmethod
    def _remove_existing_references(content: str) -> str:
        """移除现有的参考文献部分"""
        if "## References" in content:
            content = content.split("## References")[0]
        elif "## 参考文献" in content:
            content = content.split("## 参考文献")[0]
        return content.rstrip()
    
    @staticmethod
    def _generate_tech_references(contexts: Dict[str, str], content: str) -> str:
        """生成技术参考文献"""
        references = ["## 📚 References", ""]
        
        cited_refs = set(re.findall(r'\[REF_(\d+)\]', content))
        
        for i, (url, source_content) in enumerate(contexts.items(), 1):
            if str(i) in cited_refs or len(cited_refs) == 0:
                title = EngineeringFormatter._extract_tech_title(source_content, url)
                doc_type = EngineeringFormatter._identify_doc_type(url, source_content)
                
                citation = f"[REF_{i}] **{title}** - {doc_type}  \n   🔗 {url}"
                references.append(citation)
                references.append("")
        
        # 添加相关资源
        references.extend([
            "## 🔗 Additional Resources",
            "",
            "### Official Documentation",
            "- Check the official project documentation for the latest updates",
            "- Review API reference for detailed parameter descriptions",
            "",
            "### Community Resources", 
            "- GitHub Issues for troubleshooting",
            "- Stack Overflow for community support",
            "- Discord/Slack channels for real-time help",
            ""
        ])
        
        return "\n".join(references)
    
    @staticmethod
    def _extract_tech_title(content: str, url: str) -> str:
        """提取技术文档标题"""
        lines = content.strip().split('\n')
        
        for line in lines[:5]:
            line = line.strip()
            if line and not line.startswith('#'):
                title = re.sub(r'[#*\[\]]+', '', line).strip()
                if 10 < len(title) < 100:
                    return title
        
        # 从URL提取
        from urllib.parse import urlparse
        try:
            path = urlparse(url).path
            if path:
                return path.split('/')[-1].replace('-', ' ').replace('_', ' ').title()
        except:
            pass
        
        return "Technical Documentation"
    
    @staticmethod
    def _identify_doc_type(url: str, content: str) -> str:
        """识别文档类型"""
        url_lower = url.lower()
        content_lower = content.lower()
        
        if 'github.com' in url_lower:
            if 'readme' in url_lower:
                return "GitHub README"
            elif 'wiki' in url_lower:
                return "GitHub Wiki"
            else:
                return "GitHub Repository"
        elif 'stackoverflow.com' in url_lower:
            return "Stack Overflow Discussion"
        elif 'docs.' in url_lower or 'documentation' in url_lower:
            return "Official Documentation"
        elif '.pdf' in url_lower:
            return "PDF Document"
        elif 'tutorial' in content_lower or 'guide' in content_lower:
            return "Tutorial/Guide"
        elif 'api' in content_lower:
            return "API Documentation"
        else:
            return "Web Article"
    
    @staticmethod
    def _remove_duplicate_headers(content: str, topic: str) -> str:
        """移除重复的头部信息"""
        lines = content.split('\n')
        cleaned_lines = []
        skip_until_content = False
        found_first_section = False
        
        for i, line in enumerate(lines):
            line_stripped = line.strip()
            
            # 跳过重复的标题行
            if (line_stripped.startswith(f"# {topic}") or 
                line_stripped.startswith("# crawl4ai") or
                line_stripped.startswith("# Crawl4AI")):
                if found_first_section:
                    skip_until_content = True
                    continue
                else:
                    found_first_section = True
            
            # 跳过元数据和概述重复部分
            if skip_until_content and (
                line_stripped.startswith("Generated on:") or
                line_stripped.startswith("Language:") or
                line_stripped.startswith("**Generated:**") or
                line_stripped.startswith("**Document Type:**") or
                line_stripped.startswith("**Target Audience:**") or
                line_stripped.startswith("**Complexity Level:**") or
                line_stripped == "---" or
                line_stripped.startswith("## 📋 Document Overview") or
                line_stripped.startswith("### 🎯 What You'll Learn") or
                line_stripped.startswith("### 📚 Prerequisites") or
                line_stripped.startswith("This comprehensive technical guide")
            ):
                continue
            
            # 找到实际内容开始的地方（通常是第一个实际段落或章节）
            if skip_until_content and (
                line_stripped.startswith("### 1.") or
                line_stripped.startswith("## 1.") or
                (line_stripped and not line_stripped.startswith("#") and not line_stripped.startswith("**") and len(line_stripped) > 20)
            ):
                skip_until_content = False
            
            if not skip_until_content:
                cleaned_lines.append(line)
        
        return '\n'.join(cleaned_lines).strip()





