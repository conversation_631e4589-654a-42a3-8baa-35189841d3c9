# crawl4ai的使用方法和技巧 - Engineering Research Report

Generated on: 2025-07-18 22:41:44
Language: 中文

---

### 1. 概述与核心概念

Crawl4AI 是一个专为人工智能（AI）和大型语言模型（LLM）应用设计的强大、开源的异步网页爬虫和数据抓取工具 [REF_1][REF_5][REF_15]。它旨在解决现代网站动态内容加载（如JavaScript渲染）的挑战，并以LLM友好的格式（如干净的Markdown）提供抓取结果 [REF_1][REF_7]。

**核心优势：**

*   **动态内容处理：** 能够模拟真实浏览器行为，执行JavaScript并等待内容加载，从而抓取传统HTTP请求无法获取的动态生成内容 [REF_1][REF_15]。
*   **异步与并发：** 采用异步处理和并发请求机制，实现高效、快速的大规模网页抓取，显著提升性能 [REF_1][REF_15]。
*   **LLM友好输出：** 自动将HTML内容转换为结构化、简洁的Markdown格式，便于LLM进行理解、摘要和分析 [REF_5][REF_7][REF_15]。
*   **灵活的提取策略：** 支持基于CSS选择器、XPath的传统数据提取，也支持结合LLM进行更智能的内容摘要和数据抽取 [REF_7][REF_15]。
*   **可配置性：** 提供丰富的配置选项，包括浏览器行为、爬虫运行参数、页面交互、缓存模式等，满足各种复杂需求 [REF_2][REF_8][REF_9]。

**核心组件：**

*   **`AsyncWebCrawler`：** 爬虫的核心入口点，负责协调整个爬取过程 [REF_7]。
*   **`BrowserConfig`：** 用于配置底层浏览器（如Chromium）的行为，包括是否无头模式、超时设置等 [REF_7][REF_9]。
*   **`CrawlerRunConfig`：** 定义爬虫的运行参数，如最大爬取页面数、爬取深度、内容选择规则等 [REF_7]。
*   **`DefaultMarkdownGenerator`：** 内置的Markdown生成器，可将HTML转换为简洁的Markdown，并支持内容过滤 [REF_7]。
*   **`CrawlerResult`：** 封装了爬取结果，包括原始HTML、转换后的Markdown、提取的数据等 [REF_2]。

Crawl4AI 的设计理念是为开发者提供一个免费、高效、功能强大的工具，以民主化数据获取，特别适用于需要高质量、结构化网络数据来训练、增强或运行AI应用场景 [REF_15]。

### 2. 环境准备与安装

Crawl4AI 基于Python，并依赖Playwright来控制无头浏览器。因此，在安装Crawl4AI之前，需要确保Python环境和Docker（推荐用于浏览器管理）已准备就绪。

**2.1. 前置条件**

*   **Python：** 建议使用 Python 3.8 或更高版本。
*   **Docker：** 强烈推荐安装 Docker Desktop 或 Docker Engine。Crawl4AI 默认使用 Docker 容器来运行 Playwright 浏览器实例，这提供了更好的隔离性、稳定性和跨平台兼容性 [REF_1][REF_14]。

**2.2. 安装步骤**

**2.2.1. 使用 pip 安装 (推荐)**

这是最直接的安装方式。Crawl4AI 会自动安装其Python依赖项，包括 `playwright`。

```bash
# 确保你的pip是最新版本
python -m pip install --upgrade pip

# 安装 Crawl4AI 及其核心依赖
pip install crawl4ai

# 安装Playwright浏览器驱动
# 这一步会下载Chromium、Firefox和WebKit浏览器二进制文件
playwright install
```

**2.2.2. 使用 Docker 部署 (生产环境推荐)**

对于生产环境或需要更稳定、可控的浏览器运行环境，推荐使用 Docker 部署 Crawl4AI 服务。这通常涉及运行一个包含 Crawl4AI 及其所有依赖的 Docker 容器。Crawl4AI 官方提供了 Docker 镜像 [REF_1][REF_2]。

虽然 Crawl4AI 作为一个Python库可以直接导入使用，但其核心功能（如浏览器控制）通常通过与 Playwright 驱动的浏览器交互来实现。在 Docker 中运行 Crawl4AI 容器可以避免本地环境配置Playwright的复杂性，并提供一个干净、隔离的运行环境。

以下是一个简单的 `docker-compose.yml` 示例，用于启动 Crawl4AI 服务（假设你将其作为API服务封装，或者只是想运行其CLI工具）。对于库使用，你通常会在自己的应用容器中安装它。

```yaml
# docker-compose.yml
version: '3.8'
services:
  crawl4ai-service:
    # 替换为最新的Crawl4AI Docker镜像，如果Crawl4AI提供了官方的独立服务镜像
    # 通常Crawl4AI是一个库，你会在自己的应用中集成它。
    # 如果要运行Crawl4AI CLI或一个简单的API服务，你需要构建一个包含Crawl4AI的自定义镜像。
    # 假设我们构建一个简单的Python应用，它使用了crawl4ai库。
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8000:8000" # 假设你的应用在8000端口提供API
    environment:
      # 可以设置Crawl4AI相关的环境变量，例如代理设置等
      # C4A_HEADLESS_BROWSER_URL: ws://some-browser-ws-endpoint
      # C4A_DEFAULT_USER_AGENT: YourCustomUserAgent
      PYTHONUNBUFFERED: 1
    # 如果需要持久化数据，可以添加卷
    # volumes:
    #   - ./data:/app/data
    restart: unless-stopped

# Dockerfile 示例 (用于构建一个简单的Python应用，集成Crawl4AI)
# FROM python:3.9-slim-buster
# WORKDIR /app
# COPY requirements.txt .
# RUN pip install --no-cache-dir -r requirements.txt
# RUN playwright install chromium  # 安装浏览器驱动
# COPY . .
# CMD ["python", "your_app.py"]

# requirements.txt 示例
# crawl4ai
# uvicorn # 如果你构建一个FastAPI应用
# fastapi # 如果你构建一个FastAPI应用
```

**重要提示：** Crawl4AI 本身是一个 Python 库，而不是一个独立的服务。当使用 Docker 时，你通常会构建一个包含 Crawl4AI 库的自定义 Python 应用镜像，并在该容器中运行你的爬虫逻辑 [REF_10][REF_11][REF_14]。

### 3. 快速开始

本节将展示如何使用 Crawl4AI 运行你的第一个爬取任务，并生成Markdown输出。

**3.1. 最小化爬取示例**

这是一个最简单的Python脚本，用于创建一个 `AsyncWebCrawler` 实例，爬取指定URL，并打印其Markdown输出 [REF_7]。

```python
import asyncio
import logging
from crawl4ai import AsyncWebCrawler
from crawl4ai.config import CrawlerRunConfig, BrowserConfig

# 配置日志，以便在控制台看到更多信息
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def main():
    target_url = "https://www.pondhouse-data.com/blog/webcrawling-with-crawl4ai" # 示例URL [REF_1]

    # 使用 async with 语句确保爬虫资源被正确释放
    try:
        async with AsyncWebCrawler() as crawler:
            logger.info(f"开始爬取: {target_url}")
            # 执行爬取，返回 CrawlerResult 对象
            result = await crawler.run(url=target_url)

            if result:
                logger.info(f"成功爬取 {target_url}")
                # 打印前300个字符的Markdown内容
                markdown_content = result.markdown[:300] if result.markdown else "无Markdown内容"
                print("\n--- Markdown 内容 (前300字符) ---")
                print(markdown_content)
                print("---------------------------------")

                # 打印页面标题
                if result.title:
                    print(f"页面标题: {result.title}")
                else:
                    print("无页面标题")

                # 可以访问其他属性，例如原始HTML
                # print(f"原始HTML长度: {len(result.html)} 字符")
            else:
                logger.warning(f"未能获取 {target_url} 的爬取结果。")

    except Exception as e:
        logger.error(f"爬取过程中发生错误: {e}", exc_info=True)

if __name__ == "__main__":
    asyncio.run(main())
```
**代码解释：**

*   `import asyncio`：Crawl4AI 是一个异步库，因此需要 `asyncio` 来运行异步代码。
*   `from crawl4ai import AsyncWebCrawler`：导入核心爬虫类。
*   `async with AsyncWebCrawler() as crawler:`：推荐使用 `async with` 语句初始化爬虫。这确保了在爬取完成后，浏览器实例等资源能够被正确关闭和释放，避免资源泄露。
*   `await crawler.run(url=target_url)`：这是执行单个URL爬取的核心方法。它返回一个 `CrawlerResult` 对象，其中包含爬取到的数据。
*   `result.markdown`：`CrawlerResult` 对象的一个属性，包含了页面内容转换而来的Markdown格式文本 [REF_7]。
*   `logging`：添加日志输出，方便跟踪程序执行状态和调试。

**3.2. 运行示例**

1.  保存上述代码为 `quickstart_crawl.py`。
2.  在终端中运行：
    ```bash
    python quickstart_crawl.py
    ```
    首次运行时，Playwright 可能会下载浏览器二进制文件。之后，你将看到日志输出和爬取到的Markdown内容片段。

**3.3. 基础配置介绍**

Crawl4AI 允许通过 `BrowserConfig` 和 `CrawlerRunConfig` 进行精细配置 [REF_7]。

```python
import asyncio
import logging
from crawl4ai import AsyncWebCrawler
from crawl4ai.config import CrawlerRunConfig, BrowserConfig

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def main():
    target_url = "https://www.pondhouse-data.com/blog/webcrawling-with-crawl4ai"

    try:
        # 配置浏览器：无头模式，超时时间
        # headless=True: 浏览器在后台运行，不显示UI (默认值)
        # headless=False: 浏览器会显示UI，方便调试，但不适用于生产环境 [REF_9]
        browser_config = BrowserConfig(
            headless=True,
            timeout_ms=30000, # 浏览器操作超时时间，单位毫秒
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Crawl4AI-Bot/1.0"
        )

        # 配置爬虫运行：最大页面数，提取内容的选择器
        crawler_run_config = CrawlerRunConfig(
            max_pages_to_crawl=1, # 仅爬取一个页面
            # content_selectors=["article", ".post-content"], # 示例：只提取特定CSS选择器下的内容
            # 默认情况下，Crawl4AI会尝试抓取页面的主要内容，无需指定选择器
            # 如果需要更精确的控制，可以指定
            # page_load_timeout_ms=20000 # 页面加载超时时间
        )

        async with AsyncWebCrawler(browser_config=browser_config) as crawler:
            logger.info(f"开始使用自定义配置爬取: {target_url}")
            result = await crawler.run(url=target_url, config=crawler_run_config)

            if result:
                logger.info(f"成功爬取 {target_url}")
                markdown_content = result.markdown[:500] if result.markdown else "无Markdown内容"
                print("\n--- Markdown 内容 (前500字符) ---")
                print(markdown_content)
                print("---------------------------------")
                if result.title:
                    print(f"页面标题: {result.title}")
            else:
                logger.warning(f"未能获取 {target_url} 的爬取结果。")

    except Exception as e:
        logger.error(f"爬取过程中发生错误: {e}", exc_info=True)

if __name__ == "__main__":
    asyncio.run(main())
```
**为什么要配置？**
配置可以让你更好地控制爬虫行为，例如：
*   **`headless=False`：** 在开发和调试阶段，可以设置为 `False` 来观察浏览器实际的渲染过程，这对于诊断页面加载问题或元素定位问题非常有帮助 [REF_9]。
*   **`timeout_ms`：** 防止爬虫因页面加载过慢而无限等待。
*   **`user_agent`：** 模拟不同的用户代理，避免被网站识别为爬虫或获取特定设备的内容。
*   **`content_selectors`：** 仅提取页面中你关心的特定部分，减少无关信息的干扰，提高数据质量和处理效率 [REF_2][REF_9]。

### 4. 核心API详解

Crawl4AI 的核心在于其异步设计和可配置的API。本节将深入探讨 `AsyncWebCrawler`、`BrowserConfig`、`CrawlerRunConfig` 以及如何处理 `CrawlerResult`。

**4.1. `AsyncWebCrawler`**

`AsyncWebCrawler` 是 Crawl4AI 的主要工作单元。它负责管理浏览器实例、执行页面导航和内容提取。

```python
import asyncio
import logging
from crawl4ai import AsyncWebCrawler
from crawl4ai.config import BrowserConfig, CrawlerRunConfig, MarkdownGenerationConfig, ContentSelectionConfig
from crawl4ai.llms.llm_config import LLMConfig
from crawl4ai.llms.llm_types import LLMType

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def demonstrate_async_web_crawler():
    url = "https://example.com" # 替换为实际可访问的URL

    try:
        # 1. 初始化 AsyncWebCrawler
        # 可以不带参数，使用默认配置
        # async with AsyncWebCrawler() as crawler:

        # 2. 带有自定义 BrowserConfig 和 CrawlerRunConfig
        browser_config = BrowserConfig(
            headless=True,
            timeout_ms=45000, # 浏览器操作超时45秒
            user_agent="Mozilla/5.0 (compatible; Crawl4AI-Bot/1.0; +http://yourdomain.com/bot.html)"
        )

        crawler_run_config = CrawlerRunConfig(
            max_pages_to_crawl=1,
            # 可以指定内容选择器，只提取特定区域
            # content_selection_config=ContentSelectionConfig(
            #     css_selectors=["article", ".main-content"],
            #     exclude_css_selectors=[".sidebar", "footer"]
            # ),
            # Markdown 生成配置
            markdown_generation_config=MarkdownGenerationConfig(
                strip_html_tags_from_code_blocks=True,
                max_length=5000 # 限制Markdown长度
            ),
            # LLM配置示例 (如果需要LLM辅助提取)
            # llm_config=LLMConfig(
            #     llm_type=LLMType.OPENAI,
            #     api_key="YOUR_OPENAI_API_KEY",
            #     model_name="gpt-4o"
            # )
        )

        async with AsyncWebCrawler(browser_config=browser_config) as crawler:
            logger.info(f"尝试爬取: {url} (使用自定义配置)")
            result = await crawler.run(url=url, config=crawler_run_config)

            if result:
                logger.info(f"成功爬取 {url}")
                print(f"页面标题: {result.title}")
                print(f"URL: {result.url}")
                print(f"Markdown内容长度: {len(result.markdown) if result.markdown else 0}")
                print("\n--- 提取的Markdown片段 ---")
                print(result.markdown[:1000] if result.markdown else "无Markdown内容")
                print("-------------------------")

                # 访问原始HTML
                # print(f"原始HTML长度: {len(result.html)}")
            else:
                logger.warning(f"未能获取 {url} 的爬取结果。")

    except Exception as e:
        logger.error(f"AsyncWebCrawler 演示失败: {e}", exc_info=True)

if __name__ == "__main__":
    asyncio.run(demonstrate_async_web_crawler())
```
**`crawler.run()` 方法的参数：**

*   `url` (str): 目标URL。
*   `config` (CrawlerRunConfig, optional): 覆盖或扩展爬虫运行的默认配置。
*   `markdown_generator` (MarkdownGenerator, optional): 自定义Markdown生成器实例。
*   `llm_config` (LLMConfig, optional): 用于LLM相关操作（如LLM提取）的配置。

**4.2. `BrowserConfig`**

`BrowserConfig` 用于控制底层 Playwright 浏览器实例的行为 [REF_2][REF_9]。

```python
from crawl4ai.config import BrowserConfig

# 常见配置项：
# headless: 是否在无头模式下运行浏览器 (True/False)。生产环境通常为True。
# timeout_ms: 浏览器操作（如页面导航、元素查找）的超时时间，单位毫秒。
# user_agent: 设置请求的User-Agent字符串，模拟不同浏览器或设备。
# browser_type: 指定使用的浏览器类型，如 'chromium' (默认), 'firefox', 'webkit'。
# proxy: 代理服务器配置，用于绕过IP限制或地理限制。
# ignore_https_errors: 是否忽略HTTPS证书错误 (慎用)。
# disable_images: 是否禁用图片加载，可以提高速度和减少带宽。
# disable_javascript: 是否禁用JavaScript执行 (慎用，现代网站多依赖JS)。

# 示例：启用UI显示，设置较长超时，禁用图片加载
debug_browser_config = BrowserConfig(
    headless=False, # 调试时设置为False
    timeout_ms=60000, # 60秒超时
    disable_images=True,
    user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
)

# 示例：生产环境配置
prod_browser_config = BrowserConfig(
    headless=True,
    timeout_ms=30000,
    user_agent="Crawl4AI-Production-Bot/1.0",
    # proxy={"server": "http://user:<EMAIL>:8080"} # 代理配置示例
)

print(f"调试模式浏览器配置: {debug_browser_config}")
print(f"生产模式浏览器配置: {prod_browser_config}")
```
**为什么重要？**
*   **`headless`：** 调试时设为 `False` 可以直观看到页面加载过程，帮助定位问题。生产环境设为 `True` 可以节省资源，提高效率。
*   **`timeout_ms`：** 合理的超时设置能防止爬虫在 unresponsive 的页面上无限等待，提高鲁棒性。
*   **`user_agent` 和 `proxy`：** 对于反爬虫机制，模拟真实用户行为和使用代理是绕过限制的关键 [REF_6]。

**4.3. `CrawlerRunConfig`**

`CrawlerRunConfig` 控制爬虫的运行逻辑和数据处理方式 [REF_2]。

```python
from crawl4ai.config import CrawlerRunConfig, ContentSelectionConfig, MarkdownGenerationConfig
from crawl4ai.llms.llm_config import LLMConfig
from crawl4ai.llms.llm_types import LLMType

# 常见配置项：
# max_pages_to_crawl: 最大爬取页面数 (用于深度爬取)。
# max_depth: 最大爬取深度 (用于深度爬取)。
# content_selection_config: 定义需要包含或排除的内容区域的CSS选择器。
# markdown_generation_config: 配置Markdown生成行为。
# llm_config: 配置LLM模型，用于LLM辅助的数据提取或摘要。
# page_load_timeout_ms: 等待页面完全加载的超时时间。
# wait_until: 页面导航完成的条件 ('load', 'domcontentloaded', 'networkidle', 'commit')。
# scroll_down_pages: 页面向下滚动的次数，用于加载懒加载内容。

# 示例：深度爬取配置，并指定内容选择器
deep_crawl_config = CrawlerRunConfig(
    max_pages_to_crawl=10, # 最多爬取10个页面
    max_depth=2, # 爬取深度为2
    content_selection_config=ContentSelectionConfig(
        css_selectors=[".article-body", "#main-content"], # 仅抓取文章主体或主内容区
        exclude_css_selectors=["aside", "footer", ".ad-banner"] # 排除侧边栏、页脚、广告
    ),
    markdown_generation_config=MarkdownGenerationConfig(
        strip_html_tags_from_code_blocks=True,
        max_length=8000 # 限制单个页面Markdown输出最大长度
    ),
    page_load_timeout_ms=30000, # 页面加载超时30秒
    scroll_down_pages=3 # 模拟向下滚动3次，以加载动态内容
)

# 示例：使用LLM进行摘要 (需要LLM API Key)
# llm_enabled_config = CrawlerRunConfig(
#     llm_config=LLMConfig(
#         llm_type=LLMType.OPENAI,
#         api_key="YOUR_OPENAI_API_KEY",
#         model_name="gpt-3.5-turbo",
#         temperature=0.7
#     ),
#     # prompt_for_extraction="请总结页面主要内容，提取标题和作者。", # 自定义LLM提示
#     # enable_llm_extraction=True # 启用LLM提取
# )

print(f"深度爬取配置: {deep_crawl_config}")
# print(f"LLM启用配置: {llm_enabled_config}")
```
**为什么重要？**
*   **`max_pages_to_crawl` 和 `max_depth`：** 防止爬虫无限地在网站上漫游，控制爬取范围。
*   **`content_selection_config`：** 这是提取高质量数据的关键。通过精确的CSS选择器，可以只获取页面上你真正需要的信息，极大地减少噪声 [REF_2][REF_9]。
*   **`scroll_down_pages`：** 对于许多使用懒加载（lazy loading）或无限滚动（infinite scroll）的现代网站，此选项至关重要，它模拟用户滚动行为以加载更多内容 [REF_1]。

**4.4. `CrawlerResult`**

`CrawlerResult` 是 `crawler.run()` 方法的返回值，包含了爬取操作的所有结果数据 [REF_2]。

```python
import asyncio
import logging
from crawl4ai import AsyncWebCrawler
from crawl4ai.config import CrawlerRunConfig, ContentSelectionConfig

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def demonstrate_crawler_result():
    url = "https://www.example.com" # 替换为实际可访问的URL

    try:
        crawler_run_config = CrawlerRunConfig(
            content_selection_config=ContentSelectionConfig(
                css_selectors=["h1", "p"] # 尝试提取h1和p标签内容
            )
        )
        async with AsyncWebCrawler() as crawler:
            result = await crawler.run(url=url, config=crawler_run_config)

            if result:
                print("\n--- CrawlerResult 属性 ---")
                print(f"URL: {result.url}")
                print(f"最终URL (重定向后): {result.final_url}")
                print(f"标题: {result.title}")
                print(f"Markdown内容长度: {len(result.markdown) if result.markdown else 0}")
                print(f"原始HTML长度: {len(result.html) if result.html else 0}")

                # 提取的数据 (如果配置了content_selection_config且页面有匹配内容)
                if result.extracted_data:
                    print("\n--- 提取的结构化数据 ---")
                    for key, value in result.extracted_data.items():
                        print(f"{key}: {value[:100]}...") # 打印前100字符
                else:
                    print("\n无结构化数据提取 (可能因为未配置选择器或页面无匹配内容)")

                # 截图 (如果配置了截图功能)
                # if result.screenshot_base64:
                #     print(f"截图数据长度: {len(result.screenshot_base64)} 字节 (Base64编码)")

                # 页面上的所有链接 (仅限深度爬取)
                # if result.links:
                #     print(f"发现链接数: {len(result.links)}")
                #     print(f"前5个链接: {result.links[:5]}")

            else:
                logger.warning(f"获取 {url} 的结果为空。")

    except Exception as e:
        logger.error(f"CrawlerResult 演示失败: {e}", exc_info=True)

if __name__ == "__main__":
    asyncio.run(demonstrate_crawler_result())
```
**`CrawlerResult` 的关键属性：**

*   `url` (str): 初始请求的URL。
*   `final_url` (str): 经过重定向后的最终URL。
*   `title` (str): 页面的HTML标题。
*   `markdown` (str): 页面的主要内容转换为Markdown格式。
*   `html` (str): 页面完整的原始HTML内容。
*   `extracted_data` (dict): 如果配置了内容选择器，将包含按选择器键值对提取的结构化数据。
*   `screenshot_base64` (str): 页面的Base64编码截图（如果启用）。
*   `links` (list): 页面上发现的所有链接（主要用于深度爬取）。

**4.5. `DefaultMarkdownGenerator`**

Crawl4AI 默认使用 `DefaultMarkdownGenerator` 将HTML转换为Markdown。你可以通过 `MarkdownGenerationConfig` 来配置它的行为 [REF_2][REF_7]。

```python
import asyncio
import logging
from crawl4ai import AsyncWebCrawler
from crawl4ai.config import CrawlerRunConfig, MarkdownGenerationConfig

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def demonstrate_markdown_generation():
    url = "https://docs.crawl4ai.com/core/quickstart/" # Crawl4AI官方文档，内容丰富 [REF_2]

    try:
        # 配置Markdown生成器
        markdown_config = MarkdownGenerationConfig(
            # strip_html_tags_from_code_blocks=True, # 移除代码块中的HTML标签
            # use_gfm_tables=True, # 使用GitHub Flavored Markdown表格语法
            # enable_links=True, # 包含链接
            # enable_images=False, # 排除图片
            max_length=2000, # 限制Markdown输出的最大长度
            # filters=["nav", "footer", ".sidebar"], # 排除导航、页脚、侧边栏的CSS选择器
            # 也可以使用ContentSelectionConfig的exclude_css_selectors
        )

        crawler_run_config = CrawlerRunConfig(
            markdown_generation_config=markdown_config
        )

        async with AsyncWebCrawler() as crawler:
            logger.info(f"开始爬取并生成Markdown: {url}")
            result = await crawler.run(url=url, config=crawler_run_config)

            if result and result.markdown:
                print("\n--- 生成的Markdown内容 (部分) ---")
                print(result.markdown)
                print("---------------------------------")
            else:
                logger.warning(f"未能获取 {url} 的Markdown内容。")

    except Exception as e:
        logger.error(f"Markdown生成演示失败: {e}", exc_info=True)

if __name__ == "__main__":
    asyncio.run(demonstrate_markdown_generation())
```
**Markdown生成的重要性：**
*   **LLM友好：** Markdown是一种轻量级标记语言，比HTML更简洁，LLM更容易解析和理解其结构和内容 [REF_5]。
*   **去噪：** 通过配置 `filters` 或 `ContentSelectionConfig`，可以移除页面中的广告、导航、页脚等无关内容，只保留核心文章或数据，提高LLM处理效率和准确性。

### 5. 实用代码示例

本节将提供更复杂的实用示例，涵盖并发爬取、结构化数据提取以及处理动态加载内容的场景。

**5.1. 并发爬取多个URL**

Crawl4AI 的异步特性使其非常适合并发爬取多个URL，显著提高效率。

```python
import asyncio
import logging
from crawl4ai import AsyncWebCrawler
from crawl4ai.config import BrowserConfig, CrawlerRunConfig

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def concurrent_crawl_example():
    urls_to_crawl = [
        "https://www.pondhouse-data.com/blog/webcrawling-with-crawl4ai", # [REF_1]
        "https://docs.crawl4ai.com/core/quickstart/", # [REF_2]
        "https://www.example.com",
    ]

    browser_config = BrowserConfig(
        headless=True,
        timeout_ms=45000 # 增加超时时间以应对网络波动
    )

    crawler_run_config = CrawlerRunConfig(
        page_load_timeout_ms=30000,
        markdown_generation_config={"max_length": 2000} # 限制Markdown长度
    )

    results = {}
    try:
        async with AsyncWebCrawler(browser_config=browser_config) as crawler:
            tasks = []
            for url in urls_to_crawl:
                # 为每个URL创建一个爬取任务
                task = asyncio.create_task(crawler.run(url=url, config=crawler_run_config))
                tasks.append(task)

            # 等待所有任务完成
            # return_exceptions=True 会让任务在发生异常时返回异常对象，而不是中断整个 wait
            completed_tasks = await asyncio.gather(*tasks, return_exceptions=True)

            for url, result_or_exception in zip(urls_to_crawl, completed_tasks):
                if isinstance(result_or_exception, Exception):
                    logger.error(f"爬取 {url} 失败: {result_or_exception}", exc_info=False)
                    results[url] = {"status": "failed", "error": str(result_or_exception)}
                elif result_or_exception:
                    logger.info(f"成功爬取 {url}")
                    results[url] = {
                        "status": "success",
                        "title": result_or_exception.title,
                        "markdown_length": len(result_or_exception.markdown) if result_or_exception.markdown else 0,
                        "markdown_snippet": result_or_exception.markdown[:500] if result_or_exception.markdown else "无内容"
                    }
                else:
                    logger.warning(f"爬取 {url} 未返回结果。")
                    results[url] = {"status": "no_result"}

    except Exception as e:
        logger.critical(f"并发爬虫初始化或运行异常: {e}", exc_info=True)

    print("\n--- 并发爬取结果摘要 ---")
    for url, data in results.items():
        print(f"URL: {url}")
        print(f"  状态: {data.get('status')}")
        if data.get('status') == 'success':
            print(f"  标题: {data.get('title')}")
            print(f"  Markdown长度: {data.get('markdown_length')}")
            # print(f"  Markdown片段: {data.get('markdown_snippet')}") # 片段可能很长，按需打印
        elif data.get('status') == 'failed':
            print(f"  错误: {data.get('error')}")
        print("-" * 30)

if __name__ == "__main__":
    asyncio.run(concurrent_crawl_example())
```
**为什么这么做？**
*   **`asyncio.gather(*tasks, return_exceptions=True)`：** 这是Python中并发执行异步任务的强大工具。`return_exceptions=True` 确保即使某些任务失败，也不会中断整个 `gather` 调用，从而允许你处理所有任务的结果，无论是成功还是失败。
*   **资源复用：** 在 `async with AsyncWebCrawler() as crawler:` 块中，所有并发任务共享同一个 `crawler` 实例及其内部的浏览器资源，这比为每个URL创建新的爬虫实例更高效。

**5.2. 提取结构化数据 (CSS选择器)**

通过 `ContentSelectionConfig`，可以指定CSS选择器来从页面中提取特定的结构化数据 [REF_2][REF_7]。

```python
import asyncio
import logging
from crawl4ai import AsyncWebCrawler
from crawl4ai.config import CrawlerRunConfig, ContentSelectionConfig

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def structured_data_extraction_example():
    # 示例目标：从一个虚构的博客文章页面提取标题、作者和发布日期
    # 注意：此URL仅为演示，实际网站可能需要调整CSS选择器
    target_url = "https://www.pondhouse-data.com/blog/webcrawling-with-crawl4ai"

    try:
        # 定义内容选择配置
        # 键是你想在 extracted_data 中看到的字段名
        # 值是对应的CSS选择器
        content_selection_config = ContentSelectionConfig(
            css_selectors={
                "article_title": "h1.blog-post-title", # 假设文章标题在h1.blog-post-title
                "author": ".blog-post-author span", # 假设作者在.blog-post-author下的span
                "publish_date": ".blog-post-date", # 假设发布日期在.blog-post-date
                "first_paragraph": "p:first-of-type", # 提取第一个段落
                "all_paragraphs": "p" # 提取所有段落，这将返回一个列表
            },
            # 也可以排除某些元素
            exclude_css_selectors=[".comments-section", "footer"]
        )

        crawler_run_config = CrawlerRunConfig(
            content_selection_config=content_selection_config
        )

        async with AsyncWebCrawler() as crawler:
            logger.info(f"开始爬取并提取结构化数据: {target_url}")
            result = await crawler.run(url=target_url, config=crawler_run_config)

            if result and result.extracted_data:
                print("\n--- 提取的结构化数据 ---")
                for key, value in result.extracted_data.items():
                    if isinstance(value, list):
                        print(f"{key}:")
                        for item in value:
                            print(f"  - {item[:100]}...") # 打印列表项的前100字符
                    else:
                        print(f"{key}: {value[:100]}...") # 打印字符串的前100字符
            else:
                logger.warning(f"未能从 {target_url} 提取结构化数据。请检查CSS选择器是否正确或页面内容是否存在。")

    except Exception as e:
        logger.error(f"结构化数据提取示例失败: {e}", exc_info=True)

if __name__ == "__main__":
    asyncio.run(structured_data_extraction_example())
```
**为什么这么做？**
*   **精确控制：** CSS选择器允许你精确地定位页面上的特定元素。这是从非结构化网页中获取结构化数据的最常用方法。
*   **字典输出：** `extracted_data` 会以字典的形式返回，键是你定义的字段名，值是对应选择器提取到的文本内容。如果选择器匹配到多个元素，值将是一个列表。
*   **组合使用：** 你可以同时进行Markdown生成和结构化数据提取，满足不同的下游需求。

**5.3. 处理动态加载内容 (模拟滚动和等待)**

许多现代网站使用JavaScript动态加载内容，例如无限滚动或点击“加载更多”按钮。Crawl4AI 可以模拟这些用户交互 [REF_1][REF_7]。

```python
import asyncio
import logging
from crawl4ai import AsyncWebCrawler
from crawl4ai.config import CrawlerRunConfig, BrowserConfig
from crawl4ai.actions import PageInteractionConfig, Interaction

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def dynamic_content_example():
    # 替换为需要模拟滚动的动态加载内容页面
    # 例如，一个无限滚动的新闻列表或博客归档页面
    # 警告：请选择一个对爬虫友好的网站进行测试，避免对目标网站造成过大压力
    dynamic_url = "https://example.com/dynamic-content-page" # 假设的动态加载页面

    try:
        browser_config = BrowserConfig(
            headless=True,
            timeout_ms=60000 # 增加浏览器操作超时，因为需要等待内容加载
        )

        # 模拟页面交互：向下滚动3次，每次间隔2秒
        page_interaction_config = PageInteractionConfig(
            interactions=[
                Interaction(action="scroll_down", value=1), # 第一次滚动
                Interaction(action="wait", value=2000), # 等待2秒让内容加载
                Interaction(action="scroll_down", value=1), # 第二次滚动
                Interaction(action="wait", value=2000), # 等待2秒
                Interaction(action="scroll_down", value=1), # 第三次滚动
                Interaction(action="wait", value=2000), # 等待2秒
                # 也可以模拟点击按钮
                # Interaction(action="click", selector=".load-more-button")
            ]
        )

        crawler_run_config = CrawlerRunConfig(
            page_load_timeout_ms=45000, # 页面加载超时
            scroll_down_pages=3, # 也可以直接使用这个参数代替PageInteractionConfig的多次scroll_down
            # 如果网站有“加载更多”按钮，可以结合PageInteractionConfig
            # page_interaction_config=page_interaction_config # 如果使用PageInteractionConfig，则不使用scroll_down_pages
        )

        async with AsyncWebCrawler(browser_config=browser_config) as crawler:
            logger.info(f"开始爬取动态内容页面: {dynamic_url}")
            result = await crawler.run(url=dynamic_url, config=crawler_run_config)

            if result and result.markdown:
                print(f"成功爬取动态页面: {dynamic_url}")
                print(f"Markdown内容长度: {len(result.markdown)}")
                print("\n--- 动态加载内容片段 (前1000字符) ---")
                print(result.markdown[:1000])
                print("---------------------------------------")
            else:
                logger.warning(f"未能获取 {dynamic_url} 的动态内容。")

    except Exception as e:
        logger.error(f"动态内容爬取示例失败: {e}", exc_info=True)

if __name__ == "__main__":
    asyncio.run(dynamic_content_example())
```
**为什么要处理动态内容？**
*   **现代网站特性：** 许多网站为了提升用户体验，采用AJAX、React、Vue、Angular等框架，内容在页面初始加载后通过JavaScript异步获取并渲染。传统的HTTP请求无法捕获这些内容 [REF_1]。
*   **`scroll_down_pages`：** 这是一个便捷参数，用于模拟页面向下滚动指定的次数，适用于无限滚动页面 [REF_7]。
*   **`PageInteractionConfig`：** 提供更细粒度的控制，可以模拟点击按钮（如“加载更多”）、填写表单、等待特定元素出现等复杂用户行为 [REF_2][REF_9]。
*   **`Interaction(action="wait", value=ms)`：** 在执行交互后等待一段时间，给JavaScript足够的时间来加载新内容。这是处理动态内容的常见策略。

### 6. 高级功能与配置

Crawl4AI 提供了丰富的高级功能和配置选项，以应对更复杂的爬取场景。

**6.1. 深度爬取 (Deep Crawling) 与 URL 播种 (URL Seeding)**

深度爬取允许爬虫自动发现并访问页面上的链接，而URL播种则定义了爬取的起始点 [REF_2]。

```python
import asyncio
import logging
from crawl4ai import AsyncWebCrawler
from crawl4ai.config import CrawlerRunConfig, BrowserConfig
from crawl4ai.url_seeding import URLSeed

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def deep_crawl_with_seeding_example():
    # 定义初始URL种子
    url_seeds = [
        URLSeed(url="https://docs.crawl4ai.com/core/quickstart/", max_depth=1), # 从快速开始页面开始，只爬取1层深度
        URLSeed(url="https://www.example.com", max_depth=0) # 只爬取这个页面，不深入
    ]

    browser_config = BrowserConfig(
        headless=True,
        timeout_ms=60000
    )

    crawler_run_config = CrawlerRunConfig(
        max_pages_to_crawl=5, # 整个爬取任务最多爬取5个页面
        page_load_timeout_ms=30000,
        markdown_generation_config={"max_length": 1500}
    )

    try:
        async with AsyncWebCrawler(browser_config=browser_config) as crawler:
            logger.info("开始深度爬取与URL播种...")
            # run_multi 方法用于处理多个URL种子或深度爬取
            results = await crawler.run_multi(urls=url_seeds, config=crawler_run_config)

            print("\n--- 深度爬取结果摘要 ---")
            if results:
                for result in results:
                    if result:
                        print(f"URL: {result.url}")
                        print(f"  标题: {result.title}")
                        print(f"  Markdown长度: {len(result.markdown) if result.markdown else 0}")
                        print(f"  发现链接数: {len(result.links) if result.links else 0}")
                        print("-" * 30)
                    else:
                        print("某个URL种子未返回结果。")
            else:
                logger.warning("深度爬取未返回任何结果。")

    except Exception as e:
        logger.error(f"深度爬取示例失败: {e}", exc_info=True)

if __name__ == "__main__":
    asyncio.run(deep_crawl_with_seeding_example())
```
**解释：**
*   **`URLSeed`：** 允许你为每个起始URL单独指定 `max_depth`，实现更灵活的深度控制。
*   **`crawler.run_multi()`：** 这个方法接受一个 `URLSeed` 列表，是执行深度爬取或同时处理多个独立URL的入口。
*   **`max_pages_to_crawl`：** 这是一个全局限制，定义了整个 `run_multi` 调用期间最多爬取的页面总数。

**6.2. C4A-Script (自定义JavaScript执行)**

C4A-Script 允许你在页面加载后执行自定义的JavaScript代码，这对于处理复杂的用户交互或提取特定数据非常有用 [REF_2]。

```python
import asyncio
import logging
from crawl4ai import AsyncWebCrawler
from crawl4ai.config import CrawlerRunConfig
from crawl4ai.c4a_script import C4AScript

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def c4a_script_example():
    # 假设目标页面有一个隐藏的元素，需要JS点击才能显示，或者需要执行JS来计算某个值
    target_url = "https://www.example.com" # 替换为实际需要执行JS的URL

    # 定义C4A-Script
    # 这是一个JavaScript字符串，将在页面加载后执行
    # 你可以在JS中操作DOM，或者返回一个JSON对象
    # 注意：JS代码将在浏览器环境中执行，可以访问window、document等
    c4a_script_content = """
    // 模拟点击一个按钮来显示隐藏内容
    const hiddenButton = document.querySelector('#show-more-button');
    if (hiddenButton) {
        hiddenButton.click();
        console.log('Clicked show more button.');
    }

    // 等待一段时间让内容加载（如果点击触发了异步加载）
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 从页面中提取一个JS计算的值
    const dynamicValue = document.querySelector('.dynamic-data')?.innerText || 'N/A';
    const computedSum = 10 + 20;

    // 返回一个JSON对象，Crawl4AI会将其捕获到 extracted_data 中
    return {
        "dynamic_extracted_value": dynamicValue,
        "computed_sum_from_js": computedSum,
        "script_executed": true
    };
    """

    crawler_run_config = CrawlerRunConfig(
        c4a_script=C4AScript(script_content=c4a_script_content),
        page_load_timeout_ms=45000 # 增加超时以应对JS执行和内容加载
    )

    try:
        async with AsyncWebCrawler() as crawler:
            logger.info(f"开始使用C4A-Script爬取: {target_url}")
            result = await crawler.run(url=target_url, config=crawler_run_config)

            if result:
                print(f"成功爬取: {target_url}")
                print(f"页面标题: {result.title}")
                print(f"Markdown内容长度: {len(result.markdown) if result.markdown else 0}")

                if result.extracted_data:
                    print("\n--- C4A-Script 提取的数据 ---")
                    print(result.extracted_data)
                else:
                    logger.warning("C4A-Script 未返回 extracted_data 或返回为空。")

            else:
                logger.warning(f"未能获取 {target_url} 的爬取结果。")

    except Exception as e:
        logger.error(f"C4A-Script 示例失败: {e}", exc_info=True)

if __name__ == "__main__":
    asyncio.run(c4a_script_example())
```
**解释：**
*   **灵活性：** `C4AScript` 提供了对浏览器环境的完全控制。你可以执行任何JavaScript代码，包括模拟用户输入、点击、滚动，甚至修改DOM。
*   **数据返回：** 如果你的JavaScript代码返回一个JSON对象，Crawl4AI 会将其解析并添加到 `CrawlerResult` 的 `extracted_data` 属性中，与CSS选择器提取的数据合并。

**6.3. 缓存模式 (Cache Modes)**

Crawl4AI 支持多种缓存模式，可以在重复爬取相同URL时提高效率并减少对目标网站的请求 [REF_2][REF_9]。

```python
import asyncio
import logging
import os
from crawl4ai import AsyncWebCrawler
from crawl4ai.config import CrawlerRunConfig
from crawl4ai.config.cache_modes import CacheMode

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def cache_mode_example():
    target_url = "https://www.example.com"
    cache_dir = "./crawl_cache" # 缓存文件将存储在这里
    os.makedirs(cache_dir, exist_ok=True) # 确保缓存目录存在

    # 第一次运行：使用 CacheMode.CACHE_AND_FETCH
    # 如果缓存不存在，则抓取并缓存；如果存在，则返回缓存
    logger.info("--- 第一次运行 (CacheMode.CACHE_AND_FETCH) ---")
    run_config_first = CrawlerRunConfig(
        cache_mode=CacheMode.CACHE_AND_FETCH,
        cache_dir=cache_dir,
        # cache_ttl_seconds=3600 # 缓存有效期1小时
    )
    try:
        async with AsyncWebCrawler() as crawler:
            result_first = await crawler.run(url=target_url, config=run_config_first)
            if result_first:
                logger.info(f"第一次运行成功，Markdown长度: {len(result_first.markdown) if result_first.markdown else 0}")
                print(f"页面标题: {result_first.title}")
            else:
                logger.warning("第一次运行未获取结果。")
    except Exception as e:
        logger.error(f"第一次运行缓存示例失败: {e}", exc_info=True)

    await asyncio.sleep(1) # 稍作等待

    # 第二次运行：再次请求相同的URL
    # 因为缓存已存在且在有效期内，爬虫将直接从缓存中读取，不会再次访问网站
    logger.info("\n--- 第二次运行 (CacheMode.CACHE_AND_FETCH, 期望从缓存读取) ---")
    try:
        async with AsyncWebCrawler() as crawler:
            result_second = await crawler.run(url=target_url, config=run_config_first) # 使用相同的配置
            if result_second:
                logger.info(f"第二次运行成功，Markdown长度: {len(result_second.markdown) if result_second.markdown else 0}")
                print(f"页面标题: {result_second.title}")
                # 你可以通过日志判断是否从缓存加载，通常会有类似 "Loading from cache" 的信息
            else:
                logger.warning("第二次运行未获取结果。")
    except Exception as e:
        logger.error(f"第二次运行缓存示例失败: {e}", exc_info=True)

    await asyncio.sleep(1)

    # 第三次运行：使用 CacheMode.FORCE_FETCH (强制重新抓取)
    logger.info("\n--- 第三次运行 (CacheMode.FORCE_FETCH, 强制重新抓取) ---")
    run_config_force_fetch = CrawlerRunConfig(
        cache_mode=CacheMode.FORCE_FETCH,
        cache_dir=cache_dir
    )
    try:
        async with AsyncWebCrawler() as crawler:
            result_third = await crawler.run(url=target_url, config=run_config_force_fetch)
            if result_third:
                logger.info(f"第三次运行成功 (强制抓取)，Markdown长度: {len(result_third.markdown) if result_third.markdown else 0}")
                print(f"页面标题: {result_third.title}")
            else:
                logger.warning("第三次运行未获取结果。")
    except Exception as e:
        logger.error(f"第三次运行缓存示例失败: {e}", exc_info=True)

    # 清理缓存目录 (可选)
    # import shutil
    # if os.path.exists(cache_dir):
    #     shutil.rmtree(cache_dir)
    #     logger.info(f"已清理缓存目录: {cache_dir}")

if __name__ == "__main__":
    asyncio.run(cache_mode_example())
```
**缓存模式类型：**
*   **`CacheMode.NO_CACHE`：** 不使用缓存，每次都重新抓取。
*   **`CacheMode.CACHE_AND_FETCH`：** 优先从缓存读取。如果缓存不存在或过期，则抓取并更新缓存。
*   **`CacheMode.FORCE_FETCH`：** 忽略缓存，强制重新抓取，并更新缓存。
*   **`CacheMode.CACHE_ONLY`：** 仅从缓存读取。如果缓存不存在，则返回空结果。

**为什么重要？**
*   **提高效率：** 对于频繁访问相同页面的场景，缓存可以显著减少网络请求和浏览器渲染时间。
*   **减轻目标网站压力：** 减少对目标服务器的请求，降低被封锁的风险。
*   **离线访问：** 允许在没有网络连接的情况下访问之前缓存的数据。

### 7. 性能优化技巧

Crawl4AI 的设计本身就强调性能，但通过一些策略可以进一步优化其运行效率。

**7.1. 利用异步并发**

Crawl4AI 基于 `asyncio` 构建，充分利用异步并发是其性能优化的核心。

*   **同时处理多个请求：** 如“并发爬取多个URL”示例所示，使用 `asyncio.gather` 同时发起多个 `crawler.run()` 调用。
*   **共享 `AsyncWebCrawler` 实例：** 避免为每个URL创建新的 `AsyncWebCrawler` 实例，因为每个实例都会启动一个浏览器进程。在 `async with AsyncWebCrawler() as crawler:` 块内，所有并发任务共享同一个浏览器实例，从而减少资源开销和启动时间。

```python
# (参考 5.1 并发爬取多个URL 示例)
# 核心思想是：
# async with AsyncWebCrawler() as crawler:
#     tasks = [asyncio.create_task(crawler.run(url)) for url in urls]
#     results = await asyncio.gather(*tasks)
```

**7.2. 精确的内容选择**

*   **使用 `ContentSelectionConfig`：** 仅提取页面中你关心的部分，而不是整个HTML文档。这减少了后续处理的数据量，并可能通过减少DOM操作来加快渲染速度 [REF_2][REF_9]。
*   **排除无关内容：** 使用 `exclude_css_selectors` 明确排除广告、导航、页脚等不必要的内容，减少Markdown生成和LLM处理的负担。

```python
from crawl4ai.config import ContentSelectionConfig

# 仅提取文章主体和标题，并排除广告和侧边栏
content_selection_config = ContentSelectionConfig(
    css_selectors=["article.main-content", "h1.post-title"],
    exclude_css_selectors=[".ad-container", "aside.sidebar"]
)
```

**7.3. 合理配置浏览器行为**

*   **无头模式 (`headless=True`)：** 在生产环境中始终使用无头模式。显示浏览器UI会消耗大量CPU和内存资源 [REF_9]。
*   **禁用图片 (`disable_images=True`)：** 如果你不需要图片内容，禁用图片加载可以显著减少页面

---

*注：由于内容长度限制，部分内容可能被截断。建议查阅官方文档获取完整信息。*

## References

[REF_1] **Crawl4AI Tutorial: Build a Powerful Web Crawler for AI Applications Using Docker**  
   🔗 https://www.pondhouse-data.com/blog/webcrawling-with-crawl4ai

[REF_2] **Source 2**  
   🔗 https://docs.crawl4ai.com/core/quickstart/

[REF_3] **Source 3**  
   🔗 https://www.youtube.com/watch?v=xo3qK6Hg9AA

[REF_4] **Source 4**  
   🔗 https://medium.com/@speaktoharisudhan/crawling-with-crawl4ai-the-open-source-scraping-beast-9d32e6946ad4

[REF_5] **Source 5**  
   🔗 https://apidog.com/blog/crawl4ai-tutorial/

[REF_6] **Source 6**  
   🔗 https://www.bright.cn/blog/web-data/crawl4ai-and-deepseek-web-scraping

[REF_7] **[Crawl4AI 开发文档 - 帮助手册 - 教程](https://geekdaxue.co/books/Crawl4AI "Crawl4AI 开发文档 - 帮助手册 - 教程")**  
   🔗 https://geekdaxue.co/read/Crawl4AI/quickstart

[REF_8] **Source 8**  
   🔗 https://docs.crawl4ai.com/

[REF_9] **Source 9**  
   🔗 https://ai-law-agent-iee2j.ondigitalocean.app/mkdocs/tutorial/episode_03_Browser_Configurations_&_Headless_Crawling/

[REF_10] **Source 10**  
   🔗 https://gainsec.com/2025/06/07/the-quickest-and-simplest-guide-to-spinning-up-a-powerful-local-ai-stack-part-5-open-webui-to-crawl4ai-local-files/

[REF_11] **Source 11**  
   🔗 https://onedollarvps.com/zh-tw/blogs/n8n-with-crawl4ai-tutorial

[REF_12] **Source 12**  
   🔗 https://docs.agno.com/examples/concepts/tools/search/crawl4ai

[REF_13] **Source 13**  
   🔗 https://www.claudemcp.com/blog/web-scraping-ai-data-battle

[REF_14] **[stepify.tech ](https://stepify.tech/)**  
   🔗 https://stepify.tech/video/c5dw_jsGNBk/n8n-crawl4ai-scrape-any-website-in-minutes-with-no-code

[REF_15] **Crawl4AI 與 Thunderbit 深度比較：真實用戶必須知道的事**  
   🔗 https://thunderbit.com/zh-Hant/blog/crawl4ai-review-and-alternative

---

*Report generated by DeepResearch*
