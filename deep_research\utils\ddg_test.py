"""测试 DDGS 连接和搜索功能"""
from ddgs import DDGS
from ddgs.exceptions import DDGSException, RatelimitException, TimeoutException
import argparse
import sys
import json

def test_ddgs_connection(proxy=None, backend="auto"):
    """测试 DDGS 连接"""
    try:
        # 根据文档，使用 "tb" 作为 Tor 代理的别名
        if proxy == "socks5://127.0.0.1:9150":
            proxy = "tb"
        
        print(f"Testing DDGS connection with proxy: {proxy or 'None'}, backend: {backend}")
        
        ddgs = DDGS(
            proxy=proxy,
            timeout=30,
            verify=True
        )
        
        print("Testing text search...")
        results = ddgs.text(
            query="python programming",
            region="us-en",
            safesearch="moderate",
            max_results=3,
            backend=backend
        )
        
        # 转换为列表以便处理
        results_list = list(results)
        print(f"Found {len(results_list)} results")
        
        for i, result in enumerate(results_list[:2]):
            print(f"Result {i+1}:")
            if isinstance(result, dict):
                print(f"  Title: {result.get('title', 'N/A')}")
                print(f"  URL: {result.get('href', 'N/A')}")
            else:
                print(f"  {result}")
            
        print("✅ Connection successful!")
        return True
        
    except RatelimitException as e:
        print(f"❌ Rate limit exceeded: {e}")
        return False
    except TimeoutException as e:
        print(f"❌ Timeout error: {e}")
        return False
    except DDGSException as e:
        print(f"❌ DDGS error: {e}")
        return False
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        return False

def test_multiple_backends(proxy=None):
    """测试多个后端"""
    backends = ["auto", "bing", "brave", "google", "yandex"]
    
    for backend in backends:
        print(f"\n--- Testing backend: {backend} ---")
        success = test_ddgs_connection(proxy, backend)
        if success:
            print(f"✅ Backend {backend} works!")
            return True
        else:
            print(f"❌ Backend {backend} failed")
    
    return False

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Test DDGS connection")
    parser.add_argument("--proxy", help="Proxy URL (e.g., socks5://127.0.0.1:9150 or tb)")
    parser.add_argument("--backend", default="auto", help="Backend to test")
    parser.add_argument("--all", action="store_true", help="Test all backends")
    
    args = parser.parse_args()
    
    if args.all:
        success = test_multiple_backends(args.proxy)
    else:
        success = test_ddgs_connection(args.proxy, args.backend)
    
    sys.exit(0 if success else 1)
