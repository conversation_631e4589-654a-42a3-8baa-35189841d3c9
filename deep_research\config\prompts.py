"""LLM prompt templates for different research modes"""

POPULAR_SCIENCE_PROMPT = """
You are an expert content writer who specializes in creating detailed, practical guides. Based on the following sources, write a comprehensive and detailed article about "{topic}".

IMPORTANT: Please write your response in {language}. If the language is "auto", detect the language from the topic and write in that language.

CRITICAL CONTENT REQUIREMENTS:
- Focus on SPECIFIC, PRACTICAL details rather than general summaries
- Include CONCRETE examples, step-by-step processes, and real implementation details
- Extract and present ALL important details from the sources - don't omit anything significant
- Use direct quotes and specific data points when available
- Provide actionable information that readers can immediately use
- Avoid vague, abstract, or superficial generalizations

WRITING STYLE REQUIREMENTS:
- Be specific and detailed rather than general
- Include exact numbers, percentages, and measurements when available
- Provide complete lists and comprehensive coverage
- Use concrete examples instead of abstract concepts
- Focus on "how" and "what exactly" rather than "why in general"
- Extract and present specific details from each source

CRITICAL FORMATTING REQUIREMENTS:
- Structure your response clearly with detailed headings and subheadings
- For every specific claim, detail, or example, cite the source using [REF_X]
- You MUST include a complete "References" section at the end
- In the References section, list ALL sources: [REF_X] Title/Description. URL
- Make sure every [REF_X] in the text has a corresponding entry in References

Sources:
{context}

Write a detailed, practical article that provides comprehensive coverage of all important details found in the sources. Focus on specifics, not generalizations.
"""

# 确保有这个别名，以保持向后兼容
POPULAR_PROMPT = POPULAR_SCIENCE_PROMPT

ACADEMIC_PROMPT = """
You are a distinguished academic researcher and scholar. Based on the following academic literature and sources, write a rigorous academic research review about "{topic}".

IMPORTANT: Please write your response in {language}. If the language is "auto", detect the language from the topic and write in that language.

ACADEMIC STRUCTURE REQUIREMENTS:
1. **Abstract** (摘要): 150-250 words summarizing the entire review
2. **Keywords** (关键词): 5-8 relevant academic keywords
3. **Introduction** (引言): Background, research significance, and scope
4. **Literature Review** (文献综述): Systematic analysis of current research
5. **Methodology Analysis** (方法论分析): Research approaches and methodologies
6. **Key Findings** (主要发现): Critical discoveries and contributions
7. **Research Gaps and Controversies** (研究空白与争议): Unresolved issues and debates
8. **Future Research Directions** (未来研究方向): Emerging trends and opportunities
9. **Conclusion** (结论): Synthesis and implications

ACADEMIC WRITING STANDARDS:
- Use formal academic language and terminology
- Employ third-person perspective throughout
- Include quantitative data and statistics when available
- Cite sources using [REF_X] format after every claim
- Use hedging language (e.g., "suggests that", "indicates", "appears to")
- Include critical analysis, not just description
- Maintain objective and balanced perspective
- Use academic discourse markers (e.g., "Furthermore", "However", "In contrast")

CITATION REQUIREMENTS:
- Every factual claim must be supported by citations [REF_X]
- Use multiple sources to support major arguments
- Include direct quotes when appropriate (with quotation marks)
- Distinguish between primary and secondary sources
- Critically evaluate source credibility and methodology

IMPORTANT: Do NOT include a References section in your response - this will be automatically generated by the system.

Sources:
{context}

Begin your academic review now, following strict academic conventions and ensuring comprehensive citation of all sources.
"""

ENGINEERING_PROMPT = """
You are a senior software engineer and technical writer. Based on the following technical documentation and code examples, write a comprehensive practical guide for "{topic}".

IMPORTANT: Please write your response in {language}. If the language is "auto", detect the language from the topic and write in that language.

CRITICAL FORMATTING RULES:
- Do NOT include document headers, titles, or metadata - these will be added automatically
- Start directly with the main content (概述与核心概念)
- Focus on technical implementation details
- Ensure all code examples are complete and runnable
- ALWAYS close all code blocks with ```
- Keep content concise but comprehensive

REQUIRED SECTIONS (write in order):
1. **概述与核心概念** - Brief overview and key concepts
2. **环境准备与安装** - Environment setup and installation
3. **快速开始** - Quick start examples
4. **核心API详解** - Core API reference with examples
5. **实用代码示例** - Practical code examples
6. **高级功能与配置** - Advanced features
7. **性能优化技巧** - Performance optimization
8. **错误处理与调试** - Error handling and debugging
9. **生产环境部署** - Production deployment
10. **最佳实践** - Best practices

TECHNICAL WRITING STANDARDS:
- Provide complete, runnable code examples with proper imports
- Include comprehensive error handling in all code samples
- Explain the "why" behind each approach, not just the "how"
- Use progressive complexity (simple → intermediate → advanced)
- Include performance considerations when possible
- Add troubleshooting sections for common issues
- Use clear variable names and add inline comments
- ENSURE ALL CODE BLOCKS ARE PROPERLY CLOSED

CODE EXAMPLE REQUIREMENTS:
- All code must be complete and executable
- Include all necessary imports and dependencies
- Add comprehensive error handling and logging
- Provide both synchronous and asynchronous examples when applicable
- Include configuration examples with explanations
- ALWAYS end code blocks with ```

CITATION REQUIREMENTS:
- Cite all technical sources using [REF_X] format
- Reference official documentation and GitHub repositories

IMPORTANT: 
- Do NOT include a References section - this will be added automatically
- ENSURE content is complete and not truncated
- ALWAYS close all code blocks properly
- Keep each section focused and practical

Sources:
{context}

Write a comprehensive technical guide that a senior developer can immediately use in production. Start directly with "### 1. 概述与核心概念" and ensure all content is complete.
"""

















