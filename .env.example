# OpenAI API Key (if using OpenAI models)
OPENAI_API_KEY=your_openai_api_key_here

# Other LLM provider keys
ANTHROPIC_API_KEY=your_anthropic_key_here
GROQ_API_KEY=your_groq_key_here

# Google Gemini API Key
GOOGLE_API_KEY=your_google_api_key_here

# DeepSeek API Key
DEEPSEEK_API_KEY=your_deepseek_api_key_here

# DuckDuckGo 搜索设置
# 代理设置 (强烈推荐使用代理避免速率限制)
# Tor 浏览器代理: socks5://127.0.0.1:9150
# HTTP 代理: **********************:port
# SOCKS5 代理: socks5://user:pass@proxy:port
DDGS_PROXY=

# 搜索结果数量
SEARCH_RESULT_COUNT=8

# 搜索区域 (wt-wt=全球, us-en=美国, cn-zh=中国)
DDGS_REGION=wt-wt

# 安全搜索 (off=关闭, moderate=中等, strict=严格)
DDGS_SAFESEARCH=off

# 请求超时时间(秒)
DDGS_TIMEOUT=30

# 搜索延迟设置(秒)
MIN_SEARCH_DELAY=3
MAX_SEARCH_DELAY=8

# DuckDuckGo AI 模型 (可选: gpt-4o-mini, llama-3.3-70b, claude-3-haiku, o3-mini, mixtral-8x7b)
DDGS_AI_MODEL=claude-3-haiku

# 默认LLM提供商 (支持的模型:
# OpenAI: openai/gpt-4o, openai/gpt-4o-mini, openai/gpt-3.5-turbo
# Anthropic: anthropic/claude-3-5-sonnet-20241022, anthropic/claude-3-haiku-20240307
# Google: gemini/gemini-1.5-pro, gemini/gemini-1.5-flash, gemini/gemini-pro
# DeepSeek: deepseek/deepseek-chat, deepseek/deepseek-coder
# Groq: groq/llama3-70b-8192, groq/mixtral-8x7b-32768)
DEFAULT_LLM_PROVIDER=openai/gpt-4o-mini


