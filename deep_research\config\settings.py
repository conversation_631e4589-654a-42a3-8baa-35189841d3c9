"""Global configuration settings"""
import os
from dotenv import load_dotenv

load_dotenv()

# Search settings - 自适应搜索数量配置
SEARCH_RESULT_COUNT = int(os.getenv('SEARCH_RESULT_COUNT', '8'))  # 默认数量

# 不同模式的搜索数量配置
ADAPTIVE_SEARCH_CONFIG = {
    'popular': {
        'base_count': 6,      # 科普模式基础数量
        'max_count': 10,      # 最大数量
        'complexity_multiplier': 1.0  # 复杂度倍数
    },
    'academic': {
        'base_count': 20,     # 学术模式需要更多来源
        'max_count': 30,      # 最大数量
        'complexity_multiplier': 3.0  # 复杂度倍数
    },
    'engineering': {
        'base_count': 10,      # 工程模式中等数量
        'max_count': 15,      # 最大数量
        'complexity_multiplier': 1.5  # 复杂度倍数
    }
}

# 复杂度关键词 - 用于判断问题难度
COMPLEXITY_KEYWORDS = {
    'high': ['advanced', 'complex', 'detailed', 'comprehensive', 'in-depth', 'thorough', 
             '高级', '复杂', '详细', '全面', '深入', '彻底', '系统', '完整'],
    'medium': ['analysis', 'comparison', 'evaluation', 'implementation', 'development',
               '分析', '比较', '评估', '实现', '开发', '应用', '方法'],
    'low': ['basic', 'simple', 'introduction', 'overview', 'what is', 'how to',
            '基础', '简单', '介绍', '概述', '什么是', '如何', '入门']
}

DEFAULT_LLM_PROVIDER = os.getenv('DEFAULT_LLM_PROVIDER', 'gemini/gemini-2.5-flash')
OUTPUT_DIR = "output"

# DuckDuckGo settings
DDGS_PROXY = os.getenv('DDGS_PROXY')  # 代理设置，例如 "socks5://127.0.0.1:9150" 用于 Tor
DDGS_TIMEOUT = int(os.getenv('DDGS_TIMEOUT', '30'))  # 请求超时时间
DDGS_REGION = os.getenv('DDGS_REGION', 'wt-wt')  # 搜索区域，wt-wt 表示全球
DDGS_SAFESEARCH = os.getenv('DDGS_SAFESEARCH', 'off')  # 安全搜索设置

# 搜索延迟设置，避免速率限制
MIN_SEARCH_DELAY = int(os.getenv('MIN_SEARCH_DELAY', '3'))
MAX_SEARCH_DELAY = int(os.getenv('MAX_SEARCH_DELAY', '8'))

# AI模型设置
DDGS_AI_MODEL = os.getenv('DDGS_AI_MODEL', 'claude-3-haiku')  # DuckDuckGo AI 模型

# LLM API Keys
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
ANTHROPIC_API_KEY = os.getenv('ANTHROPIC_API_KEY')
GROQ_API_KEY = os.getenv('GROQ_API_KEY')
GOOGLE_API_KEY = os.getenv('GOOGLE_API_KEY')  # Gemini
DEEPSEEK_API_KEY = os.getenv('DEEPSEEK_API_KEY')

# 支持的模型列表
SUPPORTED_MODELS = {
    'openai': [
        'openai/gpt-4o',
        'openai/gpt-4o-mini',
        'openai/gpt-3.5-turbo'
    ],
    'anthropic': [
        'anthropic/claude-3-5-sonnet-20241022',
        'anthropic/claude-3-haiku-20240307',
        'anthropic/claude-3-sonnet-20240229'
    ],
    'google': [
        'gemini/gemini-2.5-pro',
        'gemini/gemini-2.5-flash',
        'gemini/gemini-2.0-flash-exp',
        'gemini/gemini-pro'
    ],
    'deepseek': [
        'deepseek/deepseek-chat',
        'deepseek/deepseek-coder'
    ],
    'groq': [
        'groq/llama3-70b-8192',
        'groq/mixtral-8x7b-32768'
    ]
}

# 语言设置
DEFAULT_OUTPUT_LANGUAGE = os.getenv('DEFAULT_OUTPUT_LANGUAGE', 'auto')  # auto 表示自动检测用户输入语言
SUPPORTED_LANGUAGES = {
    'auto': 'Auto-detect from user input',
    'zh': '中文',
    'en': 'English', 
    'ja': '日本語',
    'ko': '한국어',
    'fr': 'Français',
    'de': 'Deutsch',
    'es': 'Español',
    'ru': 'Русский'
}




