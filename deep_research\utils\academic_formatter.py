"""学术写作格式化工具"""
import re
from typing import Dict, List, Tuple

class AcademicFormatter:
    """学术论文格式化助手"""
    
    @staticmethod
    def format_academic_report(content: str, topic: str, contexts: Dict[str, str]) -> str:
        """格式化学术报告，确保符合学术规范"""
        
        # 添加学术论文头部信息
        header = AcademicFormatter._generate_academic_header(topic)
        
        # 确保包含所有必需的学术部分
        formatted_content = AcademicFormatter._ensure_academic_sections(content)
        
        # 格式化引用
        formatted_content = AcademicFormatter._format_citations(formatted_content)
        
        # 检查是否已有References部分，如果没有或不完整才添加
        if not AcademicFormatter._has_complete_references(formatted_content, contexts):
            print("📝 Adding complete academic references...")
            # 移除现有的不完整References部分
            formatted_content = AcademicFormatter._remove_existing_references(formatted_content)
            # 生成符合学术标准的参考文献
            references = AcademicFormatter._generate_academic_references(contexts, formatted_content)
            formatted_content += f"\n\n{references}"
        
        # 组合完整的学术报告
        full_report = f"{header}\n\n{formatted_content}"
        
        return full_report
    
    @staticmethod
    def _generate_academic_header(topic: str) -> str:
        """生成学术论文头部"""
        from datetime import datetime
        current_date = datetime.now().strftime("%Y-%m-%d")
        
        return f"""# {topic}: A Comprehensive Literature Review

**Generated:** {current_date}  
**Review Type:** Systematic Literature Review  
**Methodology:** Multi-source Academic Analysis  

---"""
    
    @staticmethod
    def _ensure_academic_sections(content: str) -> str:
        """确保包含所有必需的学术部分"""
        required_sections = [
            "Abstract", "摘要",
            "Keywords", "关键词", 
            "Introduction", "引言",
            "Literature Review", "文献综述",
            "Methodology", "方法论",
            "Findings", "发现", "Results", "结果",
            "Discussion", "讨论",
            "Conclusion", "结论"
        ]
        
        # 检查是否包含基本学术结构
        has_abstract = any(section in content for section in ["## Abstract", "## 摘要"])
        has_keywords = any(section in content for section in ["## Keywords", "## 关键词"])
        
        if not has_abstract:
            # 如果缺少摘要，在开头添加提示
            content = "## Abstract\n\n[Abstract section should be added here]\n\n" + content
        
        if not has_keywords:
            # 如果缺少关键词，在摘要后添加
            content = content.replace("## Abstract", "## Abstract\n\n[Abstract content]\n\n## Keywords\n\n[Keywords should be added here]")
        
        return content
    
    @staticmethod
    def _format_citations(content: str) -> str:
        """格式化引用，确保符合学术标准"""
        # 确保引用格式一致
        content = re.sub(r'\[REF_(\d+)\]', r'[REF_\1]', content)
        
        # 添加页码引用（模拟）
        content = re.sub(r'\[REF_(\d+)\]', r'[REF_\1]', content)
        
        return content
    
    @staticmethod
    def _generate_academic_references(contexts: Dict[str, str], content: str) -> str:
        """生成符合学术标准的参考文献"""
        references = ["## References", ""]
        
        # 提取所有引用的REF
        cited_refs = set(re.findall(r'\[REF_(\d+)\]', content))
        
        for i, (url, source_content) in enumerate(contexts.items(), 1):
            if str(i) in cited_refs or len(cited_refs) == 0:  # 如果被引用或没有找到引用
                # 尝试提取更多学术信息
                title = AcademicFormatter._extract_academic_title(source_content, url)
                author = AcademicFormatter._extract_author_info(source_content)
                year = AcademicFormatter._extract_publication_year(source_content, url)
                
                # 格式化为学术引用格式
                if author and year:
                    citation = f"[REF_{i}] {author} ({year}). {title}. Retrieved from {url}"
                else:
                    citation = f"[REF_{i}] {title}. Retrieved from {url}"
                
                references.append(citation)
                references.append("")  # 空行分隔
        
        return "\n".join(references)
    
    @staticmethod
    def _extract_academic_title(content: str, url: str) -> str:
        """提取学术标题"""
        lines = content.strip().split('\n')
        
        # 查找可能的学术标题
        for line in lines[:10]:
            line = line.strip()
            if line and len(line) > 20:
                # 清理标题
                title = re.sub(r'[#*\[\]]+', '', line).strip()
                if len(title) > 100:
                    title = title[:100] + "..."
                return title
        
        # 从URL提取域名作为备选
        from urllib.parse import urlparse
        try:
            domain = urlparse(url).netloc
            return f"Academic Article from {domain}"
        except:
            return "Academic Source"
    
    @staticmethod
    def _extract_author_info(content: str) -> str:
        """尝试提取作者信息"""
        # 简单的作者检测模式
        author_patterns = [
            r'[Bb]y\s+([A-Z][a-z]+\s+[A-Z][a-z]+)',
            r'[Aa]uthor[s]?:\s*([A-Z][a-z]+\s+[A-Z][a-z]+)',
            r'([A-Z][a-z]+,\s*[A-Z]\.)',
        ]
        
        for pattern in author_patterns:
            match = re.search(pattern, content[:500])  # 只在前500字符中查找
            if match:
                return match.group(1)
        
        return None
    
    @staticmethod
    def _extract_publication_year(content: str, url: str) -> str:
        """尝试提取发表年份"""
        # 查找年份模式
        year_patterns = [
            r'(20[0-2]\d)',  # 2000-2029
            r'(19[8-9]\d)',  # 1980-1999
        ]
        
        # 先在URL中查找
        for pattern in year_patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        
        # 再在内容中查找
        for pattern in year_patterns:
            matches = re.findall(pattern, content[:1000])
            if matches:
                # 返回最新的年份
                return max(matches)
        
        return None
    
    @staticmethod
    def _has_complete_references(content: str, contexts: Dict[str, str]) -> bool:
        """检查是否已有完整的参考文献"""
        import re
        
        # 检查是否有References部分
        has_refs_section = "## References" in content or "## 参考文献" in content
        if not has_refs_section:
            return False
        
        # 提取References部分
        if "## References" in content:
            refs_section = content.split("## References")[1]
        elif "## 参考文献" in content:
            refs_section = content.split("## 参考文献")[1]
        else:
            return False
        
        # 检查References数量是否与contexts匹配
        ref_count = len(re.findall(r'\[REF_\d+\]', refs_section))
        return ref_count >= len(contexts)
    
    @staticmethod
    def _remove_existing_references(content: str) -> str:
        """移除现有的References部分"""
        if "## References" in content:
            content = content.split("## References")[0]
        elif "## 参考文献" in content:
            content = content.split("## 参考文献")[0]
        return content.rstrip()

