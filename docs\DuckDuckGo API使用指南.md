
![Thumbnail](https://pypi.org/static/images/twitter.abaf4b19.webp?crossorigin=anonymous)


# ddgs

> Dux Distributed Global Search. A metasearch library that aggregates results from diverse web search services.

![Python >= 3.9](https://pypi-camo.freetls.fastly.net/bdb3281545fc43fa0baf660e9e4a286f19c4f64a/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f707974686f6e2d2533453d332e392d7265642e737667?crossorigin=anonymous)

## D.D.G.S. | Dux Distributed Global Search

A metasearch library that aggregates results from diverse web search services.

## Table of Contents

- [Install](https://pypi.org/project/ddgs/#install)
- [CLI version](https://pypi.org/project/ddgs/#cli-version)
- [DDGS search operators](https://pypi.org/project/ddgs/#ddgs-search-operators)
- [Regions](https://pypi.org/project/ddgs/#regions)
- [Engines](https://pypi.org/project/ddgs/#engines)
- [DDGS class](https://pypi.org/project/ddgs/#ddgs-class)
- [Proxy](https://pypi.org/project/ddgs/#proxy)
- [Exceptions](https://pypi.org/project/ddgs/#exceptions)
- [1. text()](https://pypi.org/project/ddgs/#1-text)
- [2. images()](https://pypi.org/project/ddgs/#2-images)
- [3. videos()](https://pypi.org/project/ddgs/#3-videos)
- [4. news()](https://pypi.org/project/ddgs/#4-news)
- [Disclaimer](https://pypi.org/project/ddgs/#disclaimer)

## Install

```
pip install -U ddgs
```

## CLI version

```
ddgs--help
```

CLI examples:

```
# text searchddgstext-q"Assyrian siege of Jerusalem"# find and download pdf files via proxyddgstext-q"Economics in one lesson filetype:pdf"-rus-en-n50-prhttps://*******:1234-d-ddeconomics_reading# using Tor Browser as a proxy (`tb` is an alias for `socks5h://127.0.0.1:9150`)ddgstext-q"'The history of the Standard Oil Company' filetype:doc"-n50-d-prtb# find and save to csvddgstext-q"'neuroscience exploring the brain' filetype:pdf"-n70-oneuroscience_list.csv# don't verify SSL when making the requestddgstext-q"Mississippi Burning"-vfalse# find and download imagesddgsimages-q"beware of false prophets"-rar-es-typephoto-n500-d# get news for the last day and save to jsonddgsnews-q"sanctions"-n100-td-ojson
```

[Go To TOP](https://pypi.org/project/ddgs/#TOP)

## DDGS search operators

|Query example|Result|
|---|---|
|cats dogs|Results about cats or dogs|
|"cats and dogs"|Results for exact term "cats and dogs". If no results are found, related results are shown.|
|cats -dogs|Fewer dogs in results|
|cats +dogs|More dogs in results|
|cats filetype:pdf|PDFs about cats. Supported file types: pdf, doc(x), xls(x), ppt(x), html|
|dogs site:example.com|Pages about dogs from example.com|
|cats -site:example.com|Pages about cats, excluding example.com|
|intitle:dogs|Page title includes the word "dogs"|
|inurl:cats|Page url includes the word "cats"|

Go To TOP

## Regions

expand```
`xa-ar for Arabia
xa-en for Arabia (en)
ar-es for Argentina
au-en for Australia
at-de for Austria
be-fr for Belgium (fr)
be-nl for Belgium (nl)
br-pt for Brazil
bg-bg for Bulgaria
ca-en for Canada
ca-fr for Canada (fr)
ct-ca for Catalan
cl-es for Chile
cn-zh for China
co-es for Colombia
hr-hr for Croatia
cz-cs for Czech Republic
dk-da for Denmark
ee-et for Estonia
fi-fi for Finland
fr-fr for France
de-de for Germany
gr-el for Greece
hk-tzh for Hong Kong
hu-hu for Hungary
in-en for India
id-id for Indonesia
id-en for Indonesia (en)
ie-en for Ireland
il-he for Israel
it-it for Italy
jp-jp for Japan
kr-kr for Korea
lv-lv for Latvia
lt-lt for Lithuania
xl-es for Latin America
my-ms for Malaysia
my-en for Malaysia (en)
mx-es for Mexico
nl-nl for Netherlands
nz-en for New Zealand
no-no for Norway
pe-es for Peru
ph-en for Philippines
ph-tl for Philippines (tl)
pl-pl for Poland
pt-pt for Portugal
ro-ro for Romania
ru-ru for Russia
sg-en for Singapore
sk-sk for Slovak Republic
sl-sl for Slovenia
za-en for South Africa
es-es for Spain
se-sv for Sweden
ch-de for Switzerland (de)
ch-fr for Switzerland (fr)
ch-it for Switzerland (it)
tw-tzh for Taiwan
th-th for Thailand
tr-tr for Turkey
ua-uk for Ukraine
uk-en for United Kingdom
us-en for United States
ue-es for United States (es)
ve-es for Venezuela
vn-vi for Vietnam`
```

---

Go To TOP

## Engines

|DDGS function|Available backends|
|---|---|
|text()|`bing`,`brave`,`duckduckgo`,`google`,`mojeek`,`mullvad_brave`,`mullvad_google`,`yandex`,`yahoo`,`wikipedia`|
|images()|`duckduckgo`|
|videos()|`duckduckgo`|
|news()|`duckduckgo`|

Go To TOP

## DDGS class

```
classDDGS:"""Dux Distributed Global Search. A metasearch library that aggregates results from diverse web search services.Args:proxy (str, optional): proxy for the HTTP client, supports http/https/socks5 protocols.example: "http://user:<EMAIL>:3128". Defaults to None.timeout (int, optional): Timeout value for the HTTP client. Defaults to 5.verify (bool): SSL verification when making the request. Defaults to True."""
```

Here is an example of initializing the DDGS class.

```
fromddgsimportDDGSresults=DDGS().text("python programming",max_results=5)print(results)
```

Go To TOP

## Proxy

Package supports http/https/socks proxies. Example:`http://user:<EMAIL>:3128`.
Use a rotating proxy. Otherwise, use a new proxy with each DDGS class initialization.

*1. The easiest way. Launch the Tor Browser*

```
ddgs=DDGS(proxy="tb",timeout=10)# "tb" is an alias for "socks5h://127.0.0.1:9150"results=ddgs.text("something you need",max_results=50)
```

*2. Use any proxy server*(*example with [iproyal rotating residential proxies](https://iproyal.com/?r=residential_proxies)*)

```
ddgs=DDGS(proxy="socks5h://user:<EMAIL>:32325",timeout=10)results=ddgs.text("something you need",max_results=50)
```

*3. The proxy can also be set using the`DDGS_PROXY`environment variable.*

```
exportDDGS_PROXY="socks5h://user:<EMAIL>:32325"
```

Go To TOP

## Exceptions

```
fromddgs.exceptionsimport(DDGSException,RatelimitException,TimeoutException,)
```

Exceptions:

- `DDGSException`: Base exception for ddgs errors.
- `RatelimitException`: Inherits from DDGSException, raised for exceeding API request rate limits.
- `TimeoutException`: Inherits from DDGSException, raised for API request timeouts.

Go To TOP

## 1. text()

```
deftext(query:str,region:str="us-en",safesearch:str="moderate",timelimit:str|None=None,max_results:int|None=10,page:int=1,backend:str|list[str]="auto",)->list[dict[str,str]]:"""DDGS text metasearch.Args:query: text search query.region: us-en, uk-en, ru-ru, etc. Defaults to us-en.safesearch: on, moderate, off. Defaults to "moderate".timelimit: d, w, m, y. Defaults to None.max_results: maximum number of results. Defaults to 10.page: page of results. Defaults to 1.backend: A single or list of backends. Defaults to "auto".Returns:List of dictionaries with search results."""
```

***Example***

```
results=DDGS().text('live free or die',region='us-en',safesearch='off',timelimit='y',page=1,backend="auto")# Searching for pdf filesresults=DDGS().text('russia filetype:pdf',region='us-en',safesearch='off',timelimit='y',page=1,backend="auto")print(results)[{"title":"News, sport, celebrities and gossip | The Sun","href":"https://www.thesun.co.uk/","body":"Get the latest news, exclusives, sport, celebrities, showbiz, politics, business and lifestyle from The Sun",},...]
```

Go To TOP

## 2. images()

```
defimages(query:str,region:str="us-en",safesearch:str="moderate",timelimit:str|None=None,max_results:int|None=10,page:int=1,backend:str|list[str]="auto",size:str|None=None,color:str|None=None,type_image:str|None=None,layout:str|None=None,license_image:str|None=None,)->list[dict[str,str]]:"""DDGS images metasearch.Args:query: images search query.region: us-en, uk-en, ru-ru, etc. Defaults to us-en.safesearch: on, moderate, off. Defaults to "moderate".timelimit: d, w, m, y. Defaults to None.max_results: maximum number of results. Defaults to 10.page: page of results. Defaults to 1.backend: A single or list of backends. Defaults to "auto".size: Small, Medium, Large, Wallpaper. Defaults to None.color: color, Monochrome, Red, Orange, Yellow, Green, Blue,Purple, Pink, Brown, Black, Gray, Teal, White. Defaults to None.type_image: photo, clipart, gif, transparent, line.Defaults to None.layout: Square, Tall, Wide. Defaults to None.license_image: any (All Creative Commons), Public (PublicDomain),Share (Free to Share and Use), ShareCommercially (Free to Share and Use Commercially),Modify (Free to Modify, Share, and Use), ModifyCommercially (Free to Modify, Share, andUse Commercially). Defaults to None.Returns:List of dictionaries with images search results."""
```

***Example***

```
results=DDGS().images(query="butterfly",region="us-en",safesearch="off",timelimit="m",page=1,backend="auto",size=None,color="Monochrome",type_image=None,layout=None,license_image=None,)print(images)[{"title":"File:The Sun by the Atmospheric Imaging Assembly of NASA's Solar ...","image":"https://upload.wikimedia.org/wikipedia/commons/b/b4/The_Sun_by_the_Atmospheric_Imaging_Assembly_of_NASA's_Solar_Dynamics_Observatory_-_20100819.jpg","thumbnail":"https://tse4.mm.bing.net/th?id=OIP.lNgpqGl16U0ft3rS8TdFcgEsEe&pid=Api","url":"https://en.wikipedia.org/wiki/File:The_Sun_by_the_Atmospheric_Imaging_Assembly_of_NASA's_Solar_Dynamics_Observatory_-_20100819.jpg","height":3860,"width":4044,"source":"Bing",},...]
```

Go To TOP

## 3. videos()

```
defvideos(query:str,region:str="us-en",safesearch:str="moderate",timelimit:str|None=None,max_results:int|None=10,page:int=1,backend:str|list[str]="auto",resolution:str|None=None,duration:str|None=None,license_videos:str|None=None,)->list[dict[str,str]]:"""DDGS videos metasearch.Args:query: videos search query.region: us-en, uk-en, ru-ru, etc. Defaults to us-en.safesearch: on, moderate, off. Defaults to "moderate".timelimit: d, w, m. Defaults to None.max_results: maximum number of results. Defaults to 10.page: page of results. Defaults to 1.backend: A single or list of backends. Defaults to "auto".resolution: high, standart. Defaults to None.duration: short, medium, long. Defaults to None.license_videos: creativeCommon, youtube. Defaults to None.Returns:List of dictionaries with videos search results."""
```

***Example***

```
results=DDGS().videos(query="cars",region="us-en",safesearch="off",timelimit="w",page=1,backend="auto",resolution="high",duration="medium",)print(results)[{"content":"https://www.youtube.com/watch?v=6901-C73P3g","description":"Watch the Best Scenes of popular Tamil Serial #Meena that airs on Sun TV. Watch all Sun TV serials immediately after the TV telecast on Sun NXT app. *Free for Indian Users only Download here: Android - http://bit.ly/SunNxtAdroid iOS: India - http://bit.ly/sunNXT Watch on the web - https://www.sunnxt.com/ Two close friends, Chidambaram ...","duration":"8:22","embed_html":'<iframe width="1280" height="720" src="https://www.youtube.com/embed/6901-C73P3g?autoplay=1" frameborder="0" allowfullscreen></iframe>',"embed_url":"https://www.youtube.com/embed/6901-C73P3g?autoplay=1","image_token":"6c070b5f0e24e5972e360d02ddeb69856202f97718ea6c5d5710e4e472310fa3","images":{"large":"https://tse4.mm.bing.net/th?id=OVF.JWBFKm1u%2fHd%2bz2e1GitsQw&pid=Api","medium":"https://tse4.mm.bing.net/th?id=OVF.JWBFKm1u%2fHd%2bz2e1GitsQw&pid=Api","motion":"","small":"https://tse4.mm.bing.net/th?id=OVF.JWBFKm1u%2fHd%2bz2e1GitsQw&pid=Api",},"provider":"Bing","published":"2024-07-03T05:30:03.0000000","publisher":"YouTube","statistics":{"viewCount":29059},"title":"Meena - Best Scenes | 02 July 2024 | Tamil Serial | Sun TV","uploader":"Sun TV",},...]
```

Go To TOP

## 4. news()

```
defnews(query:str,region:str="us-en",safesearch:str="moderate",timelimit:str|None=None,max_results:int|None=10,page:int=1,backend:str|list[str]="auto",)->list[dict[str,str]]:"""DDGS news metasearch.Args:query: news search query.region: us-en, uk-en, ru-ru, etc. Defaults to us-en.safesearch: on, moderate, off. Defaults to "moderate".timelimit: d, w, m. Defaults to None.max_results: maximum number of results. Defaults to 10.page: page of results. Defaults to 1.backend: A single or list of backends. Defaults to "auto".Returns:List of dictionaries with news search results."""
```

***Example***

```
results=DDGS().news(query="sun",region="us-en",safesearch="off",timelimit="m",page=1,backend="auto")print(results)[{"date":"2024-07-03T16:25:22+00:00","title":"Murdoch's Sun Endorses Starmer's Labour Day Before UK Vote","body":"Rupert Murdoch's Sun newspaper endorsed Keir Starmer and his opposition Labour Party to win the UK general election, a dramatic move in the British media landscape that illustrates the country's shifting political sands.","url":"https://www.msn.com/en-us/money/other/murdoch-s-sun-endorses-starmer-s-labour-day-before-uk-vote/ar-BB1plQwl","image":"https://img-s-msn-com.akamaized.net/tenant/amp/entityid/BB1plZil.img?w=2000&h=1333&m=4&q=79","source":"Bloomberg on MSN.com",},...]
```

Go To TOP

## Disclaimer

This library is for educational purposes only.
